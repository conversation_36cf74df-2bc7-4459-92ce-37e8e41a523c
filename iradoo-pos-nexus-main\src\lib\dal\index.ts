// تصدير جميع طبقات الوصول للبيانات
export { BaseDAL } from './base'
export { UsersDAL, usersDAL } from './users'
export { ProductsDAL, productsDAL } from './products'
export { SalesDAL, salesDAL } from './sales'

// سيتم إضافة المزيد من DAL classes هنا
// export { CustomersDAL, customersDAL } from './customers'
// export { SuppliersDAL, suppliersDAL } from './suppliers'
// export { CategoriesDAL, categoriesDAL } from './categories'
// export { InventoryDAL, inventoryDAL } from './inventory'
// export { PurchasesDAL, purchasesDAL } from './purchases'
// export { PaymentsDAL, paymentsDAL } from './payments'
// export { ReportsDAL, reportsDAL } from './reports'
// export { SettingsDAL, settingsDAL } from './settings'

// دالة مساعدة للحصول على جميع DAL instances
export const getAllDALInstances = () => ({
  users: usersDAL,
  products: productsDAL,
  sales: salesDAL
  // customers: customersDAL,
  // suppliers: suppliersDAL,
  // categories: categoriesDAL,
  // inventory: inventoryDAL,
  // purchases: purchasesDAL,
  // payments: paymentsDAL,
  // reports: reportsDAL,
  // settings: settingsDAL
})

// نوع البيانات لجميع DAL instances
export type DALInstances = ReturnType<typeof getAllDALInstances>
