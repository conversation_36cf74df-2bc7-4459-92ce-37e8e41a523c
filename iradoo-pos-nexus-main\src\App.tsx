import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'sonner'
import Dashboard from './pages/Dashboard'
import POSMain from './pages/POSMain'
import Sales from './pages/Sales'
import Products from './pages/Products'
import Customers from './pages/Customers'
import Reports from './pages/Reports'

const queryClient = new QueryClient()

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-background font-sans antialiased" dir="rtl">
          <Routes>
            <Route path="/" element={<POSMain />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/sales" element={<Sales />} />
            <Route path="/products" element={<Products />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/reports" element={<Reports />} />
          </Routes>
          <Toaster position="top-center" />
        </div>
      </Router>
    </QueryClientProvider>
  )
}

export default App
