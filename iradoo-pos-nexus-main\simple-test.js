// اختبار بسيط لقاعدة البيانات
import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 اختبار بسيط لقاعدة البيانات...');

try {
  // إنشاء قاعدة البيانات
  const dbPath = path.join(__dirname, 'src/database/aridoo_pos.db');
  console.log('📁 مسار قاعدة البيانات:', dbPath);
  
  const db = new Database(dbPath);
  console.log('✅ تم إنشاء قاعدة البيانات');
  
  // تفعيل الإعدادات
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');
  console.log('✅ تم تفعيل الإعدادات');
  
  // إنشاء جدول المستخدمين بشكل مباشر
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      full_name TEXT NOT NULL,
      phone TEXT,
      role TEXT DEFAULT 'employee' CHECK (role IN ('admin', 'manager', 'employee')),
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  console.log('✅ تم إنشاء جدول المستخدمين');
  
  // إدراج مستخدم تجريبي
  const insertUser = db.prepare(`
    INSERT OR IGNORE INTO users (id, email, password_hash, full_name, phone, role, is_active)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  insertUser.run(
    '00000000-0000-0000-0000-000000000001',
    '<EMAIL>',
    '$2b$10$example_hash_here',
    'مدير النظام',
    '+964770000000',
    'admin',
    1
  );
  console.log('✅ تم إدراج المستخدم الإداري');
  
  // اختبار قراءة البيانات
  const users = db.prepare('SELECT * FROM users').all();
  console.log(`📊 عدد المستخدمين: ${users.length}`);
  
  if (users.length > 0) {
    console.log(`👤 المستخدم الأول: ${users[0].full_name} (${users[0].email})`);
  }
  
  // إنشاء جدول إعدادات النظام
  db.exec(`
    CREATE TABLE IF NOT EXISTS system_settings (
      id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
      setting_key TEXT UNIQUE NOT NULL,
      setting_value TEXT,
      setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
      description TEXT,
      is_public INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
  console.log('✅ تم إنشاء جدول إعدادات النظام');
  
  // إدراج إعدادات أساسية
  const insertSetting = db.prepare(`
    INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public)
    VALUES (?, ?, ?, ?, ?)
  `);
  
  insertSetting.run('company_name', 'متجر أريدوو', 'string', 'اسم الشركة', 1);
  insertSetting.run('currency', 'IQD', 'string', 'العملة المستخدمة', 1);
  insertSetting.run('language', 'ar', 'string', 'لغة النظام', 1);
  
  console.log('✅ تم إدراج الإعدادات الأساسية');
  
  // اختبار قراءة الإعدادات
  const settings = db.prepare('SELECT * FROM system_settings').all();
  console.log(`⚙️ عدد الإعدادات: ${settings.length}`);
  
  settings.forEach(setting => {
    console.log(`  📝 ${setting.setting_key}: ${setting.setting_value}`);
  });
  
  // إنشاء جدول الفئات
  db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
      name TEXT NOT NULL,
      description TEXT,
      parent_id TEXT,
      is_active INTEGER DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_id) REFERENCES categories(id)
    )
  `);
  console.log('✅ تم إنشاء جدول الفئات');
  
  // إدراج فئة تجريبية
  const insertCategory = db.prepare(`
    INSERT OR IGNORE INTO categories (id, name, description, is_active)
    VALUES (?, ?, ?, ?)
  `);
  
  insertCategory.run(
    '10000000-0000-0000-0000-000000000001',
    'إلكترونيات',
    'الأجهزة الإلكترونية والكهربائية',
    1
  );
  console.log('✅ تم إدراج فئة تجريبية');
  
  // اختبار قراءة الفئات
  const categories = db.prepare('SELECT * FROM categories').all();
  console.log(`📂 عدد الفئات: ${categories.length}`);
  
  console.log('\n🎉 تم اختبار قاعدة البيانات بنجاح!');
  console.log('💡 قاعدة البيانات المحلية تعمل بشكل صحيح');
  
  // معلومات قاعدة البيانات
  const dbInfo = {
    path: dbPath,
    size: fs.statSync(dbPath).size,
    tables: db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all().length
  };
  
  console.log('\n📊 معلومات قاعدة البيانات:');
  console.log(`  📁 المسار: ${dbInfo.path}`);
  console.log(`  📏 الحجم: ${(dbInfo.size / 1024).toFixed(2)} KB`);
  console.log(`  📋 عدد الجداول: ${dbInfo.tables}`);
  
  db.close();
  
} catch (error) {
  console.error('❌ خطأ في اختبار قاعدة البيانات:', error);
  process.exit(1);
}
