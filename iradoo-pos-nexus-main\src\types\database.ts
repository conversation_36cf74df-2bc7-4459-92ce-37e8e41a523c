// أنواع قاعدة البيانات لنظام نقطة البيع أريدوو - SQLite
// Database Types for Aridoo POS System - SQLite

// أنواع البيانات الأساسية
export type UserRole = 'admin' | 'manager' | 'employee'
export type CustomerType = 'regular' | 'vip' | 'wholesale'
export type MovementType = 'in' | 'out' | 'adjustment' | 'transfer'
export type PaymentMethod = 'cash' | 'card' | 'credit' | 'mixed' | 'bank_transfer' | 'check'
export type SaleStatus = 'pending' | 'completed' | 'cancelled' | 'refunded'
export type PurchaseStatus = 'pending' | 'received' | 'partial' | 'cancelled'
export type ReferenceType = 'sale' | 'purchase' | 'customer' | 'supplier'
export type SettingType = 'string' | 'number' | 'boolean' | 'json'

// واجهة قاعدة البيانات المحلية SQLite
export interface Database {
  users: {
    Row: User
    Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<User, 'id' | 'created_at'>>
  }
  customers: {
    Row: Customer
    Insert: Omit<Customer, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Customer, 'id' | 'created_at'>>
  }
  suppliers: {
    Row: Supplier
    Insert: Omit<Supplier, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Supplier, 'id' | 'created_at'>>
  }
  categories: {
    Row: Category
    Insert: Omit<Category, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Category, 'id' | 'created_at'>>
  }
  products: {
    Row: Product
    Insert: Omit<Product, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Product, 'id' | 'created_at'>>
  }
  inventory: {
    Row: Inventory
    Insert: Omit<Inventory, 'id' | 'available_stock' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Inventory, 'id' | 'available_stock' | 'created_at'>>
  }
  stock_movements: {
    Row: StockMovement
    Insert: Omit<StockMovement, 'id' | 'created_at'>
    Update: Partial<Omit<StockMovement, 'id' | 'created_at'>>
  }
  sales: {
    Row: Sale
    Insert: Omit<Sale, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Sale, 'id' | 'created_at'>>
  }
  sale_items: {
    Row: SaleItem
    Insert: Omit<SaleItem, 'id' | 'created_at'>
    Update: Partial<Omit<SaleItem, 'id' | 'created_at'>>
  }
  purchases: {
    Row: Purchase
    Insert: Omit<Purchase, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<Purchase, 'id' | 'created_at'>>
  }
  purchase_items: {
    Row: PurchaseItem
    Insert: Omit<PurchaseItem, 'id' | 'created_at'>
    Update: Partial<Omit<PurchaseItem, 'id' | 'created_at'>>
  }
  payments: {
    Row: Payment
    Insert: Omit<Payment, 'id' | 'created_at'>
    Update: Partial<Omit<Payment, 'id' | 'created_at'>>
  }
  saved_reports: {
    Row: SavedReport
    Insert: Omit<SavedReport, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<SavedReport, 'id' | 'created_at'>>
  }
  system_settings: {
    Row: SystemSetting
    Insert: Omit<SystemSetting, 'id' | 'created_at' | 'updated_at'>
    Update: Partial<Omit<SystemSetting, 'id' | 'created_at'>>
  }
  activity_logs: {
    Row: ActivityLog
    Insert: Omit<ActivityLog, 'id' | 'created_at'>
    Update: Partial<Omit<ActivityLog, 'id' | 'created_at'>>
  }
}

// تعريف الأنواع الأساسية
export interface User {
  id: string
  email: string
  password_hash: string
  full_name: string
  phone?: string | null
  role: UserRole
  is_active: number // SQLite uses INTEGER for boolean (0/1)
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  customer_code: string
  name: string
  phone?: string | null
  email?: string | null
  address?: string | null
  city?: string | null
  customer_type: CustomerType
  credit_limit: number
  current_balance: number
  is_active: number // SQLite uses INTEGER for boolean (0/1)
  created_at: string
  updated_at: string
}

export interface Supplier {
  id: string
  supplier_code: string
  name: string
  contact_person?: string | null
  phone?: string | null
  email?: string | null
  address?: string | null
  city?: string | null
  payment_terms?: string | null
  is_active: number // SQLite uses INTEGER for boolean (0/1)
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  name: string
  description?: string | null
  parent_id?: string | null
  is_active: number // SQLite uses INTEGER for boolean (0/1)
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  product_code: string
  barcode?: string | null
  name: string
  description?: string | null
  category_id?: string | null
  supplier_id?: string | null
  unit_of_measure: string
  cost_price: number
  selling_price: number
  wholesale_price?: number | null
  min_stock_level: number
  max_stock_level?: number | null
  reorder_point: number
  tax_rate: number
  is_active: number // SQLite uses INTEGER for boolean (0/1)
  has_variants: number // SQLite uses INTEGER for boolean (0/1)
  image_url?: string | null
  created_at: string
  updated_at: string
}

export interface Inventory {
  id: string
  product_id: string
  current_stock: number
  reserved_stock: number
  available_stock: number // Computed column in SQLite
  last_updated: string
  location?: string | null
  batch_number?: string | null
  expiry_date?: string | null
  created_at: string
  updated_at: string
}

export interface StockMovement {
  id: string
  product_id: string
  movement_type: MovementType
  quantity: number
  unit_cost?: number | null
  reference_type?: string | null
  reference_id?: string | null
  notes?: string | null
  user_id?: string | null
  created_at: string
}

export interface Sale {
  id: string
  sale_number: string
  customer_id?: string | null
  user_id: string
  sale_date: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  change_amount: number
  payment_method: PaymentMethod
  status: SaleStatus
  notes?: string | null
  created_at: string
  updated_at: string
}

export interface SaleItem {
  id: string
  sale_id: string
  product_id: string
  quantity: number
  unit_price: number
  discount_amount: number
  tax_rate: number
  tax_amount: number
  line_total: number
  created_at: string
}

export interface Purchase {
  id: string
  purchase_number: string
  supplier_id: string
  user_id: string
  purchase_date: string
  expected_delivery_date?: string | null
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  status: PurchaseStatus
  notes?: string | null
  created_at: string
  updated_at: string
}

export interface PurchaseItem {
  id: string
  purchase_id: string
  product_id: string
  quantity_ordered: number
  quantity_received: number
  unit_cost: number
  discount_amount: number
  tax_rate: number
  tax_amount: number
  line_total: number
  created_at: string
}

export interface Payment {
  id: string
  payment_number: string
  reference_type: ReferenceType
  reference_id: string
  amount: number
  payment_method: PaymentMethod
  payment_date: string
  notes?: string | null
  user_id?: string | null
  created_at: string
}

export interface SavedReport {
  id: string
  name: string
  report_type: string
  parameters?: string | null // JSON as TEXT in SQLite
  user_id?: string | null
  is_public: number // SQLite uses INTEGER for boolean (0/1)
  created_at: string
  updated_at: string
}

export interface SystemSetting {
  id: string
  setting_key: string
  setting_value?: string | null
  setting_type: SettingType
  description?: string | null
  is_public: number // SQLite uses INTEGER for boolean (0/1)
  created_at: string
  updated_at: string
}

export interface ActivityLog {
  id: string
  user_id?: string | null
  action: string
  table_name?: string | null
  record_id?: string | null
  old_values?: string | null // JSON as TEXT in SQLite
  new_values?: string | null // JSON as TEXT in SQLite
  ip_address?: string | null
  user_agent?: string | null
  created_at: string
}
