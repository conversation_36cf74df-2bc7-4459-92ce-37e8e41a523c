// أنواع قاعدة البيانات لنظام نقطة البيع أريدوو
// Database Types for Aridoo POS System

export interface Database {
  public: {
    Tables: {
      users: {
        Row: User
        Insert: Omit<User, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<User, 'id' | 'created_at'>>
      }
      customers: {
        Row: Customer
        Insert: Omit<Customer, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Customer, 'id' | 'created_at'>>
      }
      suppliers: {
        Row: Supplier
        Insert: Omit<Supplier, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Supplier, 'id' | 'created_at'>>
      }
      categories: {
        Row: Category
        Insert: Omit<Category, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Category, 'id' | 'created_at'>>
      }
      products: {
        Row: Product
        Insert: Omit<Product, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Product, 'id' | 'created_at'>>
      }
      inventory: {
        Row: Inventory
        Insert: Omit<Inventory, 'id' | 'available_stock' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Inventory, 'id' | 'available_stock' | 'created_at'>>
      }
      stock_movements: {
        Row: StockMovement
        Insert: Omit<StockMovement, 'id' | 'created_at'>
        Update: Partial<Omit<StockMovement, 'id' | 'created_at'>>
      }
      sales: {
        Row: Sale
        Insert: Omit<Sale, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Sale, 'id' | 'created_at'>>
      }
      sale_items: {
        Row: SaleItem
        Insert: Omit<SaleItem, 'id' | 'created_at'>
        Update: Partial<Omit<SaleItem, 'id' | 'created_at'>>
      }
      purchases: {
        Row: Purchase
        Insert: Omit<Purchase, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Purchase, 'id' | 'created_at'>>
      }
      purchase_items: {
        Row: PurchaseItem
        Insert: Omit<PurchaseItem, 'id' | 'created_at'>
        Update: Partial<Omit<PurchaseItem, 'id' | 'created_at'>>
      }
      payments: {
        Row: Payment
        Insert: Omit<Payment, 'id' | 'created_at'>
        Update: Partial<Omit<Payment, 'id' | 'created_at'>>
      }
      saved_reports: {
        Row: SavedReport
        Insert: Omit<SavedReport, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SavedReport, 'id' | 'created_at'>>
      }
      system_settings: {
        Row: SystemSetting
        Insert: Omit<SystemSetting, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<SystemSetting, 'id' | 'created_at'>>
      }
      activity_logs: {
        Row: ActivityLog
        Insert: Omit<ActivityLog, 'id' | 'created_at'>
        Update: Partial<Omit<ActivityLog, 'id' | 'created_at'>>
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'admin' | 'manager' | 'employee'
      customer_type: 'regular' | 'vip' | 'wholesale'
      movement_type: 'in' | 'out' | 'adjustment' | 'transfer'
      payment_method: 'cash' | 'card' | 'credit' | 'mixed' | 'bank_transfer' | 'check'
      sale_status: 'pending' | 'completed' | 'cancelled' | 'refunded'
      purchase_status: 'pending' | 'received' | 'partial' | 'cancelled'
      reference_type: 'sale' | 'purchase' | 'customer' | 'supplier'
      setting_type: 'string' | 'number' | 'boolean' | 'json'
    }
  }
}

// تعريف الأنواع الأساسية
export interface User {
  id: string
  email: string
  password_hash: string
  full_name: string
  phone?: string
  role: 'admin' | 'manager' | 'employee'
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Customer {
  id: string
  customer_code: string
  name: string
  phone?: string
  email?: string
  address?: string
  city?: string
  customer_type: 'regular' | 'vip' | 'wholesale'
  credit_limit: number
  current_balance: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Supplier {
  id: string
  supplier_code: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  payment_terms?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  name: string
  description?: string
  parent_id?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  product_code: string
  barcode?: string
  name: string
  description?: string
  category_id?: string
  supplier_id?: string
  unit_of_measure: string
  cost_price: number
  selling_price: number
  wholesale_price?: number
  min_stock_level: number
  max_stock_level?: number
  reorder_point: number
  tax_rate: number
  is_active: boolean
  has_variants: boolean
  image_url?: string
  created_at: string
  updated_at: string
}

export interface Inventory {
  id: string
  product_id: string
  current_stock: number
  reserved_stock: number
  available_stock: number
  last_updated: string
  location?: string
  batch_number?: string
  expiry_date?: string
  created_at: string
  updated_at: string
}

export interface StockMovement {
  id: string
  product_id: string
  movement_type: 'in' | 'out' | 'adjustment' | 'transfer'
  quantity: number
  unit_cost?: number
  reference_type?: string
  reference_id?: string
  notes?: string
  user_id?: string
  created_at: string
}

export interface Sale {
  id: string
  sale_number: string
  customer_id?: string
  user_id: string
  sale_date: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  change_amount: number
  payment_method: 'cash' | 'card' | 'credit' | 'mixed'
  status: 'pending' | 'completed' | 'cancelled' | 'refunded'
  notes?: string
  created_at: string
  updated_at: string
}

export interface SaleItem {
  id: string
  sale_id: string
  product_id: string
  quantity: number
  unit_price: number
  discount_amount: number
  tax_rate: number
  tax_amount: number
  line_total: number
  created_at: string
}

export interface Purchase {
  id: string
  purchase_number: string
  supplier_id: string
  user_id: string
  purchase_date: string
  expected_delivery_date?: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  status: 'pending' | 'received' | 'partial' | 'cancelled'
  notes?: string
  created_at: string
  updated_at: string
}

export interface PurchaseItem {
  id: string
  purchase_id: string
  product_id: string
  quantity_ordered: number
  quantity_received: number
  unit_cost: number
  discount_amount: number
  tax_rate: number
  tax_amount: number
  line_total: number
  created_at: string
}

export interface Payment {
  id: string
  payment_number: string
  reference_type: 'sale' | 'purchase' | 'customer' | 'supplier'
  reference_id: string
  amount: number
  payment_method: 'cash' | 'card' | 'bank_transfer' | 'check'
  payment_date: string
  notes?: string
  user_id?: string
  created_at: string
}

export interface SavedReport {
  id: string
  name: string
  report_type: string
  parameters?: any
  user_id?: string
  is_public: boolean
  created_at: string
  updated_at: string
}

export interface SystemSetting {
  id: string
  setting_key: string
  setting_value?: string
  setting_type: 'string' | 'number' | 'boolean' | 'json'
  description?: string
  is_public: boolean
  created_at: string
  updated_at: string
}

export interface ActivityLog {
  id: string
  user_id?: string
  action: string
  table_name?: string
  record_id?: string
  old_values?: any
  new_values?: any
  ip_address?: string
  user_agent?: string
  created_at: string
}
