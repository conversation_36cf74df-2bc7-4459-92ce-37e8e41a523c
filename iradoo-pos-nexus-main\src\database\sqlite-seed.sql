-- البيانات الأولية لنظام نقطة البيع أريدوو - SQLite
-- Initial Data for Aridoo POS System - SQLite

-- إدراج المستخدم الإداري الافتراضي
INSERT OR IGNORE INTO users (id, email, password_hash, full_name, phone, role, is_active) VALUES
('00000000-0000-0000-0000-000000000001', '<EMAIL>', '$2b$10$example_hash_here', 'مدير النظام', '+964770000000', 'admin', 1);

-- إدراج فئات الأصناف الافتراضية
INSERT OR IGNORE INTO categories (id, name, description, is_active) VALUES
('10000000-0000-0000-0000-000000000001', 'إلكترونيات', 'الأجهزة الإلكترونية والكهربائية', 1),
('10000000-0000-0000-0000-000000000002', 'ملابس', 'الملابس والأزياء', 1),
('10000000-0000-0000-0000-000000000003', 'أغذية', 'المواد الغذائية والمشروبات', 1),
('10000000-0000-0000-0000-000000000004', 'مستحضرات تجميل', 'مستحضرات التجميل والعناية الشخصية', 1),
('10000000-0000-0000-0000-000000000005', 'أدوات منزلية', 'الأدوات والمعدات المنزلية', 1),
('10000000-0000-0000-0000-000000000006', 'كتب وقرطاسية', 'الكتب والأدوات المكتبية', 1),
('10000000-0000-0000-0000-000000000007', 'ألعاب', 'ألعاب الأطفال والترفيه', 1),
('10000000-0000-0000-0000-000000000008', 'رياضة', 'المعدات والأدوات الرياضية', 1);

-- إدراج موردين افتراضيين
INSERT OR IGNORE INTO suppliers (id, supplier_code, name, contact_person, phone, email, address, city, is_active) VALUES
('20000000-0000-0000-0000-000000000001', 'SUP001', 'شركة التقنية المتقدمة', 'أحمد محمد', '+964770111111', '<EMAIL>', 'شارع الكرادة', 'بغداد', 1),
('20000000-0000-0000-0000-000000000002', 'SUP002', 'مؤسسة الأزياء العصرية', 'فاطمة علي', '+964771222222', '<EMAIL>', 'شارع المنصور', 'بغداد', 1),
('20000000-0000-0000-0000-000000000003', 'SUP003', 'شركة الغذاء الصحي', 'محمد حسن', '+964772333333', '<EMAIL>', 'منطقة الدورة', 'بغداد', 1),
('20000000-0000-0000-0000-000000000004', 'SUP004', 'مكتبة المعرفة', 'سارة أحمد', '+964773444444', '<EMAIL>', 'شارع المتنبي', 'بغداد', 1),
('20000000-0000-0000-0000-000000000005', 'SUP005', 'شركة الرياضة والصحة', 'علي حسين', '+964774555555', '<EMAIL>', 'منطقة الجادرية', 'بغداد', 1);

-- إدراج عملاء افتراضيين
INSERT OR IGNORE INTO customers (id, customer_code, name, phone, email, address, city, customer_type, credit_limit, current_balance, is_active) VALUES
('30000000-0000-0000-0000-000000000001', 'CUST001', 'عميل نقدي', NULL, NULL, NULL, NULL, 'regular', 0, 0, 1),
('30000000-0000-0000-0000-000000000002', 'CUST002', 'علي أحمد الكاظمي', '+964770444444', '<EMAIL>', 'حي الجامعة', 'بغداد', 'regular', 500000, 0, 1),
('30000000-0000-0000-0000-000000000003', 'CUST003', 'سارة محمد النجار', '+964771555555', '<EMAIL>', 'حي الكرادة', 'بغداد', 'vip', 1000000, 0, 1),
('30000000-0000-0000-0000-000000000004', 'CUST004', 'محمد عبدالله الحسني', '+964772666666', '<EMAIL>', 'حي المنصور', 'بغداد', 'wholesale', 2000000, 0, 1),
('30000000-0000-0000-0000-000000000005', 'CUST005', 'فاطمة حسن العلوي', '+964773777777', '<EMAIL>', 'حي الأعظمية', 'بغداد', 'regular', 300000, 0, 1);

-- إدراج منتجات تجريبية
INSERT OR IGNORE INTO products (id, product_code, barcode, name, description, category_id, supplier_id, unit_of_measure, cost_price, selling_price, wholesale_price, min_stock_level, reorder_point, tax_rate, is_active, has_variants) VALUES
-- إلكترونيات
('40000000-0000-0000-0000-000000000001', 'PROD001', '1234567890123', 'هاتف ذكي سامسونج', 'هاتف ذكي بمواصفات عالية', '10000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000001', 'piece', 300000, 450000, 400000, 5, 10, 0, 1, 0),
('40000000-0000-0000-0000-000000000002', 'PROD002', '1234567890124', 'لابتوب ديل', 'لابتوب للأعمال والدراسة', '10000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000001', 'piece', 800000, 1200000, 1100000, 3, 5, 0, 1, 0),
('40000000-0000-0000-0000-000000000003', 'PROD003', '1234567890125', 'سماعات بلوتوث', 'سماعات لاسلكية عالية الجودة', '10000000-0000-0000-0000-000000000001', '20000000-0000-0000-0000-000000000001', 'piece', 50000, 80000, 70000, 10, 15, 0, 1, 0),

-- ملابس
('40000000-0000-0000-0000-000000000004', 'PROD004', '1234567890126', 'قميص رجالي', 'قميص قطني عالي الجودة', '10000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000002', 'piece', 25000, 40000, 35000, 20, 30, 0, 1, 1),
('40000000-0000-0000-0000-000000000005', 'PROD005', '1234567890127', 'فستان نسائي', 'فستان أنيق للمناسبات', '10000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000002', 'piece', 60000, 100000, 85000, 15, 20, 0, 1, 1),
('40000000-0000-0000-0000-000000000006', 'PROD006', '1234567890128', 'حذاء رياضي', 'حذاء مريح للرياضة', '10000000-0000-0000-0000-000000000002', '20000000-0000-0000-0000-000000000002', 'pair', 40000, 65000, 55000, 12, 18, 0, 1, 1),

-- أغذية
('40000000-0000-0000-0000-000000000007', 'PROD007', '1234567890129', 'أرز بسمتي', 'أرز عالي الجودة', '10000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000003', 'kg', 3000, 5000, 4500, 50, 100, 0, 1, 0),
('40000000-0000-0000-0000-000000000008', 'PROD008', '1234567890130', 'زيت طبخ', 'زيت نباتي صحي', '10000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000003', 'liter', 8000, 12000, 11000, 30, 50, 0, 1, 0),
('40000000-0000-0000-0000-000000000009', 'PROD009', '1234567890131', 'شاي أحمر', 'شاي عراقي أصيل', '10000000-0000-0000-0000-000000000003', '20000000-0000-0000-0000-000000000003', 'pack', 5000, 8000, 7000, 25, 40, 0, 1, 0),

-- مستحضرات تجميل
('40000000-0000-0000-0000-000000000010', 'PROD010', '1234567890132', 'كريم مرطب', 'كريم للعناية بالبشرة', '10000000-0000-0000-0000-000000000004', '20000000-0000-0000-0000-000000000001', 'piece', 15000, 25000, 22000, 20, 30, 0, 1, 0);

-- إدراج مخزون أولي للمنتجات
INSERT OR IGNORE INTO inventory (product_id, current_stock, reserved_stock, location) VALUES
('40000000-0000-0000-0000-000000000001', 15, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000002', 8, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000003', 25, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000004', 50, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000005', 30, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000006', 20, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000007', 200, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000008', 80, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000009', 60, 0, 'المخزن الرئيسي'),
('40000000-0000-0000-0000-000000000010', 40, 0, 'المخزن الرئيسي');

-- إدراج إعدادات النظام الافتراضية
INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('company_name', 'متجر أريدوو', 'string', 'اسم الشركة', 1),
('company_address', 'بغداد، العراق', 'string', 'عنوان الشركة', 1),
('company_phone', '+964770000000', 'string', 'هاتف الشركة', 1),
('company_email', '<EMAIL>', 'string', 'بريد الشركة الإلكتروني', 1),
('currency', 'IQD', 'string', 'العملة المستخدمة', 1),
('currency_symbol', 'د.ع', 'string', 'رمز العملة', 1),
('tax_rate', '0', 'number', 'معدل الضريبة الافتراضي', 1),
('receipt_footer', 'شكراً لزيارتكم - أريدوو', 'string', 'نص أسفل الفاتورة', 1),
('low_stock_alert', 'true', 'boolean', 'تنبيه المخزون المنخفض', 0),
('auto_backup', 'true', 'boolean', 'النسخ الاحتياطي التلقائي', 0),
('backup_interval', '24', 'number', 'فترة النسخ الاحتياطي بالساعات', 0),
('max_discount_percent', '50', 'number', 'أقصى نسبة خصم مسموحة', 0),
('allow_negative_stock', 'false', 'boolean', 'السماح بالمخزون السالب', 0),
('default_payment_method', 'cash', 'string', 'طريقة الدفع الافتراضية', 0),
('print_receipt_auto', 'true', 'boolean', 'طباعة الفاتورة تلقائياً', 0),
('language', 'ar', 'string', 'لغة النظام', 1),
('theme', 'light', 'string', 'مظهر النظام', 0),
('items_per_page', '20', 'number', 'عدد العناصر في الصفحة', 0),
('session_timeout', '480', 'number', 'انتهاء الجلسة بالدقائق', 0),
('enable_barcode_scanner', 'true', 'boolean', 'تفعيل قارئ الباركود', 0);

-- إدراج حركات مخزون أولية
INSERT OR IGNORE INTO stock_movements (product_id, movement_type, quantity, reference_type, notes, user_id) VALUES
('40000000-0000-0000-0000-000000000001', 'in', 15, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000002', 'in', 8, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000003', 'in', 25, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000004', 'in', 50, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000005', 'in', 30, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000006', 'in', 20, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000007', 'in', 200, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000008', 'in', 80, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000009', 'in', 60, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001'),
('40000000-0000-0000-0000-000000000010', 'in', 40, 'adjustment', 'مخزون أولي', '00000000-0000-0000-0000-000000000001');
