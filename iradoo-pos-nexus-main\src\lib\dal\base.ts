import { db } from '../database'
import type { Database } from 'better-sqlite3'

// فئة أساسية لطبقة الوصول للبيانات
export abstract class BaseDAL<T> {
  protected db: Database
  protected tableName: string

  constructor(tableName: string) {
    this.db = db
    this.tableName = tableName
  }

  // دالة للحصول على جميع السجلات
  getAll(): T[] {
    try {
      const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} ORDER BY created_at DESC`)
      return stmt.all() as T[]
    } catch (error) {
      console.error(`خطأ في الحصول على جميع السجلات من ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة للحصول على سجل بالمعرف
  getById(id: string): T | null {
    try {
      const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`)
      return stmt.get(id) as T | null
    } catch (error) {
      console.error(`خطأ في الحصول على السجل ${id} من ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة للبحث بشروط مخصصة
  findWhere(conditions: Record<string, any>): T[] {
    try {
      const keys = Object.keys(conditions)
      const whereClause = keys.map(key => `${key} = ?`).join(' AND ')
      const values = keys.map(key => conditions[key])
      
      const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE ${whereClause}`)
      return stmt.all(...values) as T[]
    } catch (error) {
      console.error(`خطأ في البحث في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة للحصول على سجل واحد بشروط مخصصة
  findOneWhere(conditions: Record<string, any>): T | null {
    try {
      const results = this.findWhere(conditions)
      return results.length > 0 ? results[0] : null
    } catch (error) {
      console.error(`خطأ في البحث عن سجل واحد في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة لإدراج سجل جديد
  insert(data: Omit<T, 'id' | 'created_at' | 'updated_at'>): T {
    try {
      const keys = Object.keys(data)
      const placeholders = keys.map(() => '?').join(', ')
      const values = keys.map(key => (data as any)[key])
      
      const stmt = this.db.prepare(`
        INSERT INTO ${this.tableName} (${keys.join(', ')}) 
        VALUES (${placeholders})
      `)
      
      const result = stmt.run(...values)
      return this.getById(result.lastInsertRowid as string)!
    } catch (error) {
      console.error(`خطأ في إدراج سجل في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة لتحديث سجل
  update(id: string, data: Partial<Omit<T, 'id' | 'created_at'>>): T | null {
    try {
      const keys = Object.keys(data)
      const setClause = keys.map(key => `${key} = ?`).join(', ')
      const values = keys.map(key => (data as any)[key])
      
      const stmt = this.db.prepare(`
        UPDATE ${this.tableName} 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `)
      
      const result = stmt.run(...values, id)
      return result.changes > 0 ? this.getById(id) : null
    } catch (error) {
      console.error(`خطأ في تحديث السجل ${id} في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة لحذف سجل
  delete(id: string): boolean {
    try {
      const stmt = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`)
      const result = stmt.run(id)
      return result.changes > 0
    } catch (error) {
      console.error(`خطأ في حذف السجل ${id} من ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة للحصول على عدد السجلات
  count(conditions?: Record<string, any>): number {
    try {
      let query = `SELECT COUNT(*) as count FROM ${this.tableName}`
      let values: any[] = []
      
      if (conditions) {
        const keys = Object.keys(conditions)
        const whereClause = keys.map(key => `${key} = ?`).join(' AND ')
        values = keys.map(key => conditions[key])
        query += ` WHERE ${whereClause}`
      }
      
      const stmt = this.db.prepare(query)
      const result = stmt.get(...values) as { count: number }
      return result.count
    } catch (error) {
      console.error(`خطأ في عد السجلات في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة للحصول على السجلات مع التصفح
  paginate(page: number = 1, limit: number = 10, conditions?: Record<string, any>) {
    try {
      const offset = (page - 1) * limit
      let query = `SELECT * FROM ${this.tableName}`
      let countQuery = `SELECT COUNT(*) as count FROM ${this.tableName}`
      let values: any[] = []
      
      if (conditions) {
        const keys = Object.keys(conditions)
        const whereClause = keys.map(key => `${key} = ?`).join(' AND ')
        values = keys.map(key => conditions[key])
        query += ` WHERE ${whereClause}`
        countQuery += ` WHERE ${whereClause}`
      }
      
      query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`
      
      const stmt = this.db.prepare(query)
      const countStmt = this.db.prepare(countQuery)
      
      const data = stmt.all(...values, limit, offset) as T[]
      const totalResult = countStmt.get(...values) as { count: number }
      const total = totalResult.count
      
      return {
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      }
    } catch (error) {
      console.error(`خطأ في التصفح في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة للبحث النصي
  search(searchTerm: string, searchFields: string[]): T[] {
    try {
      const searchConditions = searchFields.map(field => `${field} LIKE ?`).join(' OR ')
      const searchValues = searchFields.map(() => `%${searchTerm}%`)
      
      const stmt = this.db.prepare(`
        SELECT * FROM ${this.tableName} 
        WHERE ${searchConditions}
        ORDER BY created_at DESC
      `)
      
      return stmt.all(...searchValues) as T[]
    } catch (error) {
      console.error(`خطأ في البحث في ${this.tableName}:`, error)
      throw error
    }
  }

  // دالة لتنفيذ استعلام مخصص
  executeQuery(query: string, params: any[] = []): any[] {
    try {
      const stmt = this.db.prepare(query)
      return stmt.all(...params)
    } catch (error) {
      console.error(`خطأ في تنفيذ الاستعلام: ${query}`, error)
      throw error
    }
  }

  // دالة لتنفيذ معاملة
  transaction<R>(callback: () => R): R {
    const transaction = this.db.transaction(callback)
    return transaction()
  }
}
