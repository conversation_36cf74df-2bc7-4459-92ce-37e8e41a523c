import { BaseDAL } from './base'
import type { Sale, SaleItem } from '../../types/database'

export class SalesDAL extends BaseDAL<Sale> {
  constructor() {
    super('sales')
  }

  // دالة للحصول على مبيعة برقم المبيعة
  getBySaleNumber(saleNumber: string): Sale | null {
    try {
      const stmt = this.db.prepare('SELECT * FROM sales WHERE sale_number = ?')
      return stmt.get(saleNumber) as Sale | null
    } catch (error) {
      console.error('خطأ في الحصول على المبيعة برقم المبيعة:', error)
      throw error
    }
  }

  // دالة لإنشاء مبيعة جديدة مع تفاصيلها
  createSale(saleData: {
    customer_id?: string
    user_id: string
    items: Array<{
      product_id: string
      quantity: number
      unit_price: number
      discount_amount?: number
      tax_rate?: number
    }>
    discount_amount?: number
    payment_method?: 'cash' | 'card' | 'credit' | 'mixed'
    paid_amount?: number
    notes?: string
  }): Sale {
    return this.transaction(() => {
      try {
        // توليد رقم المبيعة
        const saleNumber = this.generateSaleNumber()

        // حساب المجاميع
        let subtotal = 0
        let totalTaxAmount = 0
        
        const processedItems = saleData.items.map(item => {
          const discountAmount = item.discount_amount || 0
          const taxRate = item.tax_rate || 0
          const lineSubtotal = (item.quantity * item.unit_price) - discountAmount
          const taxAmount = lineSubtotal * (taxRate / 100)
          const lineTotal = lineSubtotal + taxAmount

          subtotal += lineSubtotal
          totalTaxAmount += taxAmount

          return {
            ...item,
            discount_amount: discountAmount,
            tax_rate: taxRate,
            tax_amount: taxAmount,
            line_total: lineTotal
          }
        })

        const discountAmount = saleData.discount_amount || 0
        const totalAmount = subtotal + totalTaxAmount - discountAmount
        const paidAmount = saleData.paid_amount || totalAmount
        const changeAmount = Math.max(0, paidAmount - totalAmount)

        // إنشاء المبيعة
        const sale = this.insert({
          sale_number: saleNumber,
          customer_id: saleData.customer_id || null,
          user_id: saleData.user_id,
          subtotal,
          tax_amount: totalTaxAmount,
          discount_amount: discountAmount,
          total_amount: totalAmount,
          paid_amount: paidAmount,
          change_amount: changeAmount,
          payment_method: saleData.payment_method || 'cash',
          status: 'completed',
          notes: saleData.notes || null
        })

        // إدراج تفاصيل المبيعة
        const insertSaleItem = this.db.prepare(`
          INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, discount_amount, tax_rate, tax_amount, line_total)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `)

        processedItems.forEach(item => {
          insertSaleItem.run(
            sale.id,
            item.product_id,
            item.quantity,
            item.unit_price,
            item.discount_amount,
            item.tax_rate,
            item.tax_amount,
            item.line_total
          )
        })

        return sale
      } catch (error) {
        console.error('خطأ في إنشاء المبيعة:', error)
        throw error
      }
    })
  }

  // دالة لتوليد رقم المبيعة
  private generateSaleNumber(): string {
    try {
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      
      const prefix = `SAL-${year}${month}${day}`
      
      // البحث عن آخر رقم في نفس اليوم
      const stmt = this.db.prepare(`
        SELECT sale_number FROM sales 
        WHERE sale_number LIKE ? 
        ORDER BY sale_number DESC 
        LIMIT 1
      `)
      
      const lastSale = stmt.get(`${prefix}%`) as { sale_number: string } | undefined
      
      let sequence = 1
      if (lastSale) {
        const lastSequence = parseInt(lastSale.sale_number.split('-').pop() || '0')
        sequence = lastSequence + 1
      }
      
      return `${prefix}-${String(sequence).padStart(4, '0')}`
    } catch (error) {
      console.error('خطأ في توليد رقم المبيعة:', error)
      throw error
    }
  }

  // دالة للحصول على تفاصيل المبيعة
  getSaleWithItems(saleId: string): any {
    try {
      const sale = this.getById(saleId)
      if (!sale) return null

      const itemsStmt = this.db.prepare(`
        SELECT si.*, p.name as product_name, p.product_code, p.barcode
        FROM sale_items si
        JOIN products p ON si.product_id = p.id
        WHERE si.sale_id = ?
        ORDER BY si.created_at
      `)
      
      const items = itemsStmt.all(saleId)

      const customerStmt = this.db.prepare(`
        SELECT c.name as customer_name, c.phone as customer_phone
        FROM customers c
        WHERE c.id = ?
      `)
      
      const customer = sale.customer_id ? customerStmt.get(sale.customer_id) : null

      return {
        ...sale,
        items,
        customer
      }
    } catch (error) {
      console.error('خطأ في الحصول على تفاصيل المبيعة:', error)
      throw error
    }
  }

  // دالة للحصول على المبيعات حسب العميل
  getSalesByCustomer(customerId: string): Sale[] {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM sales 
        WHERE customer_id = ? 
        ORDER BY sale_date DESC
      `)
      return stmt.all(customerId) as Sale[]
    } catch (error) {
      console.error('خطأ في الحصول على مبيعات العميل:', error)
      throw error
    }
  }

  // دالة للحصول على المبيعات حسب المستخدم
  getSalesByUser(userId: string, startDate?: string, endDate?: string): Sale[] {
    try {
      let query = 'SELECT * FROM sales WHERE user_id = ?'
      const params: any[] = [userId]

      if (startDate) {
        query += ' AND sale_date >= ?'
        params.push(startDate)
      }

      if (endDate) {
        query += ' AND sale_date <= ?'
        params.push(endDate)
      }

      query += ' ORDER BY sale_date DESC'

      const stmt = this.db.prepare(query)
      return stmt.all(...params) as Sale[]
    } catch (error) {
      console.error('خطأ في الحصول على مبيعات المستخدم:', error)
      throw error
    }
  }

  // دالة للحصول على المبيعات اليومية
  getDailySales(date?: string): any {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0]
      
      const stmt = this.db.prepare(`
        SELECT 
          COUNT(*) as total_sales,
          SUM(total_amount) as total_revenue,
          SUM(paid_amount) as total_paid,
          AVG(total_amount) as average_sale,
          SUM(CASE WHEN payment_method = 'cash' THEN total_amount ELSE 0 END) as cash_sales,
          SUM(CASE WHEN payment_method = 'card' THEN total_amount ELSE 0 END) as card_sales,
          SUM(CASE WHEN payment_method = 'credit' THEN total_amount ELSE 0 END) as credit_sales
        FROM sales 
        WHERE DATE(sale_date) = ? AND status = 'completed'
      `)
      
      const summary = stmt.get(targetDate)

      // تفاصيل المبيعات
      const salesStmt = this.db.prepare(`
        SELECT s.*, c.name as customer_name, u.full_name as user_name
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE DATE(s.sale_date) = ? AND s.status = 'completed'
        ORDER BY s.sale_date DESC
      `)
      
      const sales = salesStmt.all(targetDate)

      return {
        date: targetDate,
        summary,
        sales
      }
    } catch (error) {
      console.error('خطأ في الحصول على المبيعات اليومية:', error)
      throw error
    }
  }

  // دالة لإلغاء المبيعة
  cancelSale(saleId: string, userId: string, reason?: string): boolean {
    return this.transaction(() => {
      try {
        // تحديث حالة المبيعة
        const updateSale = this.db.prepare(`
          UPDATE sales 
          SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP 
          WHERE id = ? AND status = 'completed'
        `)
        
        const result = updateSale.run(saleId)
        if (result.changes === 0) {
          return false
        }

        // إعادة المخزون
        const saleItems = this.db.prepare(`
          SELECT product_id, quantity FROM sale_items WHERE sale_id = ?
        `).all(saleId) as Array<{ product_id: string; quantity: number }>

        const updateInventory = this.db.prepare(`
          UPDATE inventory 
          SET current_stock = current_stock + ?, last_updated = CURRENT_TIMESTAMP
          WHERE product_id = ?
        `)

        const insertStockMovement = this.db.prepare(`
          INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, notes, user_id)
          VALUES (?, 'in', ?, 'sale_cancellation', ?, ?, ?)
        `)

        saleItems.forEach(item => {
          updateInventory.run(item.quantity, item.product_id)
          insertStockMovement.run(
            item.product_id,
            item.quantity,
            saleId,
            reason || 'إلغاء المبيعة',
            userId
          )
        })

        return true
      } catch (error) {
        console.error('خطأ في إلغاء المبيعة:', error)
        throw error
      }
    })
  }

  // دالة للحصول على تقرير المبيعات
  getSalesReport(startDate: string, endDate: string): any {
    try {
      const summaryStmt = this.db.prepare(`
        SELECT 
          COUNT(*) as total_sales,
          SUM(total_amount) as total_revenue,
          SUM(tax_amount) as total_tax,
          SUM(discount_amount) as total_discount,
          AVG(total_amount) as average_sale,
          MIN(total_amount) as min_sale,
          MAX(total_amount) as max_sale
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ? AND status = 'completed'
      `)
      
      const summary = summaryStmt.get(startDate, endDate)

      // المبيعات حسب طريقة الدفع
      const paymentMethodStmt = this.db.prepare(`
        SELECT 
          payment_method,
          COUNT(*) as count,
          SUM(total_amount) as total
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ? AND status = 'completed'
        GROUP BY payment_method
      `)
      
      const byPaymentMethod = paymentMethodStmt.all(startDate, endDate)

      // المبيعات اليومية
      const dailyStmt = this.db.prepare(`
        SELECT 
          DATE(sale_date) as date,
          COUNT(*) as sales_count,
          SUM(total_amount) as daily_total
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ? AND status = 'completed'
        GROUP BY DATE(sale_date)
        ORDER BY date
      `)
      
      const dailySales = dailyStmt.all(startDate, endDate)

      return {
        period: { startDate, endDate },
        summary,
        byPaymentMethod,
        dailySales
      }
    } catch (error) {
      console.error('خطأ في الحصول على تقرير المبيعات:', error)
      throw error
    }
  }
}

// إنشاء مثيل واحد للاستخدام
export const salesDAL = new SalesDAL()
