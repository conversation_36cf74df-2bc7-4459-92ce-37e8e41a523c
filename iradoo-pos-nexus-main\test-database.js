// اختبار قاعدة البيانات المحلية
// Test Local Database

import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 اختبار قاعدة البيانات المحلية...');

try {
  // إنشاء اتصال قاعدة البيانات
  const dbPath = path.join(__dirname, 'src/database/aridoo_pos.db');
  const db = new Database(dbPath);
  
  console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
  
  // تفعيل WAL mode
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');
  
  console.log('✅ تم تفعيل إعدادات قاعدة البيانات');
  
  // قراءة وتنفيذ مخطط قاعدة البيانات
  const schemaPath = path.join(__dirname, 'src/database/sqlite-schema.sql');
  if (fs.existsSync(schemaPath)) {
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // تقسيم الاستعلامات
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📋 تنفيذ ${statements.length} استعلام من مخطط قاعدة البيانات...`);
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          db.exec(statement);
        } catch (error) {
          if (!error.message.includes('already exists')) {
            console.warn('⚠️ تحذير:', error.message);
          }
        }
      }
    }
    
    console.log('✅ تم إنشاء مخطط قاعدة البيانات');
  }
  
  // قراءة وتنفيذ البيانات الأولية
  const seedPath = path.join(__dirname, 'src/database/sqlite-seed.sql');
  if (fs.existsSync(seedPath)) {
    const seedData = fs.readFileSync(seedPath, 'utf8');
    
    const statements = seedData
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📊 إدراج ${statements.length} استعلام من البيانات الأولية...`);
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          db.exec(statement);
        } catch (error) {
          if (!error.message.includes('UNIQUE constraint failed')) {
            console.warn('⚠️ تحذير:', error.message);
          }
        }
      }
    }
    
    console.log('✅ تم إدراج البيانات الأولية');
  }
  
  // اختبار الجداول
  console.log('\n📋 اختبار الجداول:');
  
  const tables = [
    'users', 'customers', 'suppliers', 'categories', 
    'products', 'inventory', 'sales', 'system_settings'
  ];
  
  for (const table of tables) {
    try {
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
      console.log(`  ✅ ${table}: ${count.count} سجل`);
    } catch (error) {
      console.log(`  ❌ ${table}: خطأ - ${error.message}`);
    }
  }
  
  // اختبار المستخدم الإداري
  console.log('\n👤 اختبار المستخدم الإداري:');
  const admin = db.prepare('SELECT * FROM users WHERE role = ? LIMIT 1').get('admin');
  if (admin) {
    console.log(`  ✅ المستخدم الإداري: ${admin.full_name} (${admin.email})`);
  } else {
    console.log('  ❌ لم يتم العثور على المستخدم الإداري');
  }
  
  // اختبار إعدادات النظام
  console.log('\n⚙️ اختبار إعدادات النظام:');
  const settings = db.prepare('SELECT setting_key, setting_value FROM system_settings LIMIT 5').all();
  settings.forEach(setting => {
    console.log(`  ✅ ${setting.setting_key}: ${setting.setting_value}`);
  });
  
  // اختبار المنتجات
  console.log('\n📦 اختبار المنتجات:');
  const products = db.prepare('SELECT name, selling_price FROM products LIMIT 3').all();
  products.forEach(product => {
    console.log(`  ✅ ${product.name}: ${product.selling_price} د.ع`);
  });
  
  console.log('\n🎉 تم اختبار قاعدة البيانات بنجاح!');
  console.log('💡 يمكنك الآن استخدام النظام بقاعدة البيانات المحلية');
  
  db.close();
  
} catch (error) {
  console.error('❌ خطأ في اختبار قاعدة البيانات:', error);
  process.exit(1);
}
