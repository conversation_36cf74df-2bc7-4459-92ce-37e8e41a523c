import { BaseDAL } from './base'
import type { Product } from '../../types/database'

export class ProductsDAL extends BaseDAL<Product> {
  constructor() {
    super('products')
  }

  // دالة للحصول على منتج بالكود
  getByCode(productCode: string): Product | null {
    try {
      const stmt = this.db.prepare('SELECT * FROM products WHERE product_code = ?')
      return stmt.get(productCode) as Product | null
    } catch (error) {
      console.error('خطأ في الحصول على المنتج بالكود:', error)
      throw error
    }
  }

  // دالة للحصول على منتج بالباركود
  getByBarcode(barcode: string): Product | null {
    try {
      const stmt = this.db.prepare('SELECT * FROM products WHERE barcode = ?')
      return stmt.get(barcode) as Product | null
    } catch (error) {
      console.error('خطأ في الحصول على المنتج بالباركود:', error)
      throw error
    }
  }

  // دالة لإنشاء منتج جديد
  createProduct(productData: {
    product_code: string
    barcode?: string
    name: string
    description?: string
    category_id?: string
    supplier_id?: string
    unit_of_measure?: string
    cost_price: number
    selling_price: number
    wholesale_price?: number
    min_stock_level?: number
    max_stock_level?: number
    reorder_point?: number
    tax_rate?: number
    image_url?: string
  }): Product {
    try {
      // التحقق من عدم تكرار الكود
      const existingProduct = this.getByCode(productData.product_code)
      if (existingProduct) {
        throw new Error('كود المنتج مستخدم من قبل')
      }

      // التحقق من عدم تكرار الباركود
      if (productData.barcode) {
        const existingBarcode = this.getByBarcode(productData.barcode)
        if (existingBarcode) {
          throw new Error('الباركود مستخدم من قبل')
        }
      }

      const newProduct = {
        ...productData,
        unit_of_measure: productData.unit_of_measure || 'piece',
        min_stock_level: productData.min_stock_level || 0,
        reorder_point: productData.reorder_point || 0,
        tax_rate: productData.tax_rate || 0,
        is_active: 1,
        has_variants: 0
      }

      return this.insert(newProduct)
    } catch (error) {
      console.error('خطأ في إنشاء المنتج:', error)
      throw error
    }
  }

  // دالة للحصول على المنتجات النشطة
  getActiveProducts(): Product[] {
    try {
      const stmt = this.db.prepare('SELECT * FROM products WHERE is_active = 1 ORDER BY name')
      return stmt.all() as Product[]
    } catch (error) {
      console.error('خطأ في الحصول على المنتجات النشطة:', error)
      throw error
    }
  }

  // دالة للحصول على المنتجات حسب الفئة
  getProductsByCategory(categoryId: string): Product[] {
    try {
      const stmt = this.db.prepare(`
        SELECT p.*, c.name as category_name 
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.category_id = ? AND p.is_active = 1
        ORDER BY p.name
      `)
      return stmt.all(categoryId) as Product[]
    } catch (error) {
      console.error('خطأ في الحصول على المنتجات حسب الفئة:', error)
      throw error
    }
  }

  // دالة للحصول على المنتجات حسب المورد
  getProductsBySupplier(supplierId: string): Product[] {
    try {
      const stmt = this.db.prepare(`
        SELECT p.*, s.name as supplier_name 
        FROM products p
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.supplier_id = ? AND p.is_active = 1
        ORDER BY p.name
      `)
      return stmt.all(supplierId) as Product[]
    } catch (error) {
      console.error('خطأ في الحصول على المنتجات حسب المورد:', error)
      throw error
    }
  }

  // دالة للبحث في المنتجات
  searchProducts(searchTerm: string): Product[] {
    return this.search(searchTerm, ['name', 'description', 'product_code', 'barcode'])
  }

  // دالة للحصول على المنتجات مع معلومات المخزون
  getProductsWithInventory(): any[] {
    try {
      const stmt = this.db.prepare(`
        SELECT p.*, 
               i.current_stock,
               i.reserved_stock,
               i.available_stock,
               c.name as category_name,
               s.name as supplier_name
        FROM products p
        LEFT JOIN inventory i ON p.id = i.product_id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        WHERE p.is_active = 1
        ORDER BY p.name
      `)
      return stmt.all()
    } catch (error) {
      console.error('خطأ في الحصول على المنتجات مع المخزون:', error)
      throw error
    }
  }

  // دالة للحصول على المنتجات منخفضة المخزون
  getLowStockProducts(): any[] {
    try {
      const stmt = this.db.prepare(`
        SELECT p.*, 
               i.current_stock,
               i.available_stock,
               c.name as category_name
        FROM products p
        LEFT JOIN inventory i ON p.id = i.product_id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_active = 1 
        AND (i.current_stock <= p.reorder_point OR i.current_stock IS NULL)
        ORDER BY (CAST(i.current_stock AS REAL) / NULLIF(p.reorder_point, 0)) ASC
      `)
      return stmt.all()
    } catch (error) {
      console.error('خطأ في الحصول على المنتجات منخفضة المخزون:', error)
      throw error
    }
  }

  // دالة لتحديث أسعار المنتج
  updatePrices(productId: string, prices: {
    cost_price?: number
    selling_price?: number
    wholesale_price?: number
  }): Product | null {
    try {
      return this.update(productId, prices)
    } catch (error) {
      console.error('خطأ في تحديث أسعار المنتج:', error)
      throw error
    }
  }

  // دالة لتفعيل/إلغاء تفعيل المنتج
  toggleProductStatus(productId: string): boolean {
    try {
      const product = this.getById(productId)
      if (!product) {
        return false
      }

      const newStatus = product.is_active ? 0 : 1
      const stmt = this.db.prepare(`
        UPDATE products 
        SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `)
      
      const result = stmt.run(newStatus, productId)
      return result.changes > 0
    } catch (error) {
      console.error('خطأ في تغيير حالة المنتج:', error)
      throw error
    }
  }

  // دالة للحصول على إحصائيات المنتجات
  getProductStats() {
    try {
      const totalProducts = this.count()
      const activeProducts = this.count({ is_active: 1 })
      const lowStockCount = this.getLowStockProducts().length

      // إحصائيات حسب الفئة
      const categoryStats = this.executeQuery(`
        SELECT c.name as category_name, COUNT(p.id) as product_count
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
        WHERE c.is_active = 1
        GROUP BY c.id, c.name
        ORDER BY product_count DESC
      `)

      // متوسط الأسعار
      const priceStats = this.executeQuery(`
        SELECT 
          AVG(cost_price) as avg_cost_price,
          AVG(selling_price) as avg_selling_price,
          MIN(selling_price) as min_price,
          MAX(selling_price) as max_price
        FROM products 
        WHERE is_active = 1
      `)[0]

      return {
        total: totalProducts,
        active: activeProducts,
        inactive: totalProducts - activeProducts,
        lowStock: lowStockCount,
        byCategory: categoryStats,
        priceStats
      }
    } catch (error) {
      console.error('خطأ في الحصول على إحصائيات المنتجات:', error)
      throw error
    }
  }

  // دالة للحصول على أفضل المنتجات مبيعاً
  getTopSellingProducts(limit: number = 10, days: number = 30): any[] {
    try {
      const stmt = this.db.prepare(`
        SELECT p.*, 
               SUM(si.quantity) as total_sold,
               SUM(si.line_total) as total_revenue,
               COUNT(DISTINCT si.sale_id) as sale_count
        FROM products p
        JOIN sale_items si ON p.id = si.product_id
        JOIN sales s ON si.sale_id = s.id
        WHERE s.status = 'completed' 
        AND s.sale_date >= datetime('now', '-${days} days')
        AND p.is_active = 1
        GROUP BY p.id
        ORDER BY total_sold DESC
        LIMIT ?
      `)
      return stmt.all(limit)
    } catch (error) {
      console.error('خطأ في الحصول على أفضل المنتجات مبيعاً:', error)
      throw error
    }
  }
}

// إنشاء مثيل واحد للاستخدام
export const productsDAL = new ProductsDAL()
