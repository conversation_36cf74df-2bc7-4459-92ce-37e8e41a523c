{"hash": "f9ef1325", "configHash": "53336b64", "lockfileHash": "946e0d98", "browserHash": "d958ec44", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "338da351", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2b9503ee", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "94fda211", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "dd95be93", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "9d0933d4", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "21226189", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "73a9b335", "needsInterop": false}}, "chunks": {"chunk-NPMM3WDP": {"file": "chunk-NPMM3WDP.js"}, "chunk-YXQGYGZK": {"file": "chunk-YXQGYGZK.js"}, "chunk-G2OWGING": {"file": "chunk-G2OWGING.js"}}}