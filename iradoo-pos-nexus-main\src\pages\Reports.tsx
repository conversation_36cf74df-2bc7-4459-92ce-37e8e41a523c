import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowRight, 
  Download, 
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  BarChart3,
  PieChart,
  FileText
} from 'lucide-react'

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('today')

  // Sample data for demonstration
  const salesData = {
    today: {
      revenue: 1250.75,
      transactions: 15,
      avgTransaction: 83.38,
      growth: 12.5
    },
    week: {
      revenue: 8750.50,
      transactions: 105,
      avgTransaction: 83.33,
      growth: 8.2
    },
    month: {
      revenue: 35200.25,
      transactions: 420,
      avgTransaction: 83.81,
      growth: -2.1
    }
  }

  const topProducts = [
    { name: 'قلم أزرق', sales: 45, revenue: 112.50 },
    { name: 'دفتر A4', sales: 12, revenue: 180.00 },
    { name: 'مبراة', sales: 30, revenue: 112.50 },
    { name: 'مسطرة 30 سم', sales: 8, revenue: 66.00 }
  ]

  const currentData = salesData[selectedPeriod as keyof typeof salesData]

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                <ArrowRight className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-800">التقارير والإحصائيات</h1>
          </div>
          <Button>
            <Download className="w-4 h-4 ml-2" />
            تصدير التقرير
          </Button>
        </div>

        {/* Period Selection */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <Calendar className="w-5 h-5 text-gray-600" />
              <span className="font-medium">فترة التقرير:</span>
              <div className="flex gap-2">
                {[
                  { key: 'today', label: 'اليوم' },
                  { key: 'week', label: 'هذا الأسبوع' },
                  { key: 'month', label: 'هذا الشهر' }
                ].map(period => (
                  <Button
                    key={period.key}
                    variant={selectedPeriod === period.key ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedPeriod(period.key)}
                  >
                    {period.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-green-600">
                    {currentData.revenue.toFixed(2)} ر.س
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    {currentData.growth > 0 ? (
                      <TrendingUp className="w-4 h-4 text-green-500" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-500" />
                    )}
                    <span className={`text-sm ${currentData.growth > 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {Math.abs(currentData.growth)}%
                    </span>
                  </div>
                </div>
                <DollarSign className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">عدد المعاملات</p>
                  <p className="text-2xl font-bold">{currentData.transactions}</p>
                  <p className="text-sm text-gray-500 mt-1">معاملة</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">متوسط المعاملة</p>
                  <p className="text-2xl font-bold">{currentData.avgTransaction.toFixed(2)} ر.س</p>
                  <p className="text-sm text-gray-500 mt-1">للمعاملة الواحدة</p>
                </div>
                <BarChart3 className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">عدد العملاء</p>
                  <p className="text-2xl font-bold">4</p>
                  <p className="text-sm text-gray-500 mt-1">عميل مسجل</p>
                </div>
                <Users className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                أفضل المنتجات مبيعاً
              </CardTitle>
              <CardDescription>
                المنتجات الأكثر مبيعاً في الفترة المحددة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{product.name}</p>
                        <p className="text-sm text-gray-500">{product.sales} قطعة</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="font-bold text-green-600">{product.revenue.toFixed(2)} ر.س</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sales Chart Placeholder */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                توزيع المبيعات
              </CardTitle>
              <CardDescription>
                توزيع المبيعات حسب الفئات
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <PieChart className="w-12 h-12 mx-auto mb-2" />
                  <p>مخطط توزيع المبيعات</p>
                  <p className="text-sm">سيتم إضافة المخططات قريباً</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              التقارير السريعة
            </CardTitle>
            <CardDescription>
              تقارير جاهزة للتصدير والطباعة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <BarChart3 className="w-6 h-6" />
                <span>تقرير المبيعات اليومية</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <Package className="w-6 h-6" />
                <span>تقرير المخزون</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <Users className="w-6 h-6" />
                <span>تقرير العملاء</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <DollarSign className="w-6 h-6" />
                <span>تقرير الأرباح</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <TrendingUp className="w-6 h-6" />
                <span>تقرير النمو</span>
              </Button>
              
              <Button variant="outline" className="h-20 flex flex-col gap-2">
                <FileText className="w-6 h-6" />
                <span>تقرير شامل</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Reports
