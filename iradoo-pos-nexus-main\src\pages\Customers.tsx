import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowRight, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Users,
  Phone,
  Mail,
  MapPin,
  ShoppingBag
} from 'lucide-react'

interface Customer {
  id: string
  name: string
  phone: string
  email: string
  address: string
  totalPurchases: number
  lastPurchase: string
  status: 'active' | 'inactive'
}

const Customers = () => {
  const [customers, setCustomers] = useState<Customer[]>([
    {
      id: '1',
      name: 'أحمد محمد علي',
      phone: '0501234567',
      email: '<EMAIL>',
      address: 'الرياض، حي النخيل',
      totalPurchases: 1250.75,
      lastPurchase: '2024-01-15',
      status: 'active'
    },
    {
      id: '2',
      name: 'فاطمة أحمد',
      phone: '0509876543',
      email: '<EMAIL>',
      address: 'جدة، حي الصفا',
      totalPurchases: 890.50,
      lastPurchase: '2024-01-10',
      status: 'active'
    },
    {
      id: '3',
      name: 'محمد عبدالله',
      phone: '0555555555',
      email: '<EMAIL>',
      address: 'الدمام، حي الفيصلية',
      totalPurchases: 2100.25,
      lastPurchase: '2024-01-20',
      status: 'active'
    },
    {
      id: '4',
      name: 'سارة خالد',
      phone: '0544444444',
      email: '<EMAIL>',
      address: 'مكة، حي العزيزية',
      totalPurchases: 450.00,
      lastPurchase: '2023-12-15',
      status: 'inactive'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const deleteCustomer = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      setCustomers(customers.filter(c => c.id !== id))
    }
  }

  const getCustomerLevel = (totalPurchases: number) => {
    if (totalPurchases >= 2000) return { label: 'عميل ذهبي', color: 'default' }
    if (totalPurchases >= 1000) return { label: 'عميل فضي', color: 'secondary' }
    return { label: 'عميل عادي', color: 'outline' }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                <ArrowRight className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-800">إدارة العملاء</h1>
          </div>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="w-4 h-4 ml-2" />
            إضافة عميل جديد
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي العملاء</p>
                  <p className="text-2xl font-bold">{customers.length}</p>
                </div>
                <Users className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">العملاء النشطون</p>
                  <p className="text-2xl font-bold text-green-600">
                    {customers.filter(c => c.status === 'active').length}
                  </p>
                </div>
                <Users className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">العملاء الذهبيون</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {customers.filter(c => c.totalPurchases >= 2000).length}
                  </p>
                </div>
                <Users className="w-8 h-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {customers.reduce((sum, c) => sum + c.totalPurchases, 0).toFixed(0)} ر.س
                  </p>
                </div>
                <ShoppingBag className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث عن عميل بالاسم أو الهاتف أو البريد الإلكتروني..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Customers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCustomers.map(customer => {
            const customerLevel = getCustomerLevel(customer.totalPurchases)
            return (
              <Card key={customer.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{customer.name}</CardTitle>
                    <Badge variant={customerLevel.color as any}>
                      {customerLevel.label}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Phone className="w-4 h-4" />
                      <span>{customer.phone}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="w-4 h-4" />
                      <span>{customer.email}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{customer.address}</span>
                    </div>

                    <div className="border-t pt-3">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">إجمالي المشتريات:</span>
                        <span className="font-bold text-green-600">
                          {customer.totalPurchases.toFixed(2)} ر.س
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-sm text-gray-600">آخر شراء:</span>
                        <span className="text-sm">{customer.lastPurchase}</span>
                      </div>

                      <div className="flex justify-between items-center">
                        <Badge variant={customer.status === 'active' ? 'default' : 'secondary'}>
                          {customer.status === 'active' ? 'نشط' : 'غير نشط'}
                        </Badge>
                        
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="outline" 
                                  onClick={() => deleteCustomer(customer.id)}>
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {filteredCustomers.length === 0 && (
          <Card>
            <CardContent className="text-center py-8 text-gray-500">
              لا توجد عملاء تطابق البحث
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default Customers
