import { createClient } from '@supabase/supabase-js'
import type { Database } from '../types/database'

// تكوين Supabase
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

// إنشاء عميل Supabase مع تحديد نوع قاعدة البيانات
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// دالة للتحقق من الاتصال بقاعدة البيانات
export const checkConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('system_settings')
      .select('setting_key')
      .limit(1)
    
    if (error) {
      console.error('خطأ في الاتصال بقاعدة البيانات:', error)
      return false
    }
    
    return true
  } catch (error) {
    console.error('خطأ في الاتصال:', error)
    return false
  }
}

// دالة لتسجيل الدخول
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

// دالة لتسجيل الخروج
export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    throw new Error(error.message)
  }
}

// دالة للحصول على المستخدم الحالي
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return user
}

// دالة للحصول على بيانات المستخدم من جدول users
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

// دالة لتحديث بيانات المستخدم
export const updateUserProfile = async (userId: string, updates: any) => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

// دالة للحصول على إعدادات النظام
export const getSystemSettings = async () => {
  const { data, error } = await supabase
    .from('system_settings')
    .select('*')
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

// دالة لتحديث إعدادات النظام
export const updateSystemSetting = async (key: string, value: string) => {
  const { data, error } = await supabase
    .from('system_settings')
    .upsert({
      setting_key: key,
      setting_value: value,
      updated_at: new Date().toISOString()
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}
