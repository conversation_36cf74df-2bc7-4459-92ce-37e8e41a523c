// إدارة قاعدة البيانات المحلية
// Local Database Management

import { db, initializeDatabase, getDatabaseInfo, createBackup, restoreBackup, optimizeDatabase, closeDatabase } from './database'
import { getAllDALInstances } from './dal'
import type { Database } from '../types/database'

// تهيئة قاعدة البيانات
initializeDatabase()

// تصدير اتصال قاعدة البيانات
export { db }

// تصدير جميع DAL instances
export const dal = getAllDALInstances()

// تصدير دوال إدارة قاعدة البيانات
export {
  initializeDatabase,
  getDatabaseInfo,
  createBackup,
  restoreBackup,
  optimizeDatabase,
  closeDatabase
}

// دالة للتحقق من الاتصال بقاعدة البيانات
export const checkConnection = () => {
  try {
    // اختبار بسيط لقاعدة البيانات
    const result = db.prepare('SELECT 1 as test').get()
    return result && (result as any).test === 1
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error)
    return false
  }
}

// دالة لتسجيل الدخول
export const signIn = async (email: string, password: string) => {
  try {
    const user = dal.users.verifyPassword(email, password)
    if (!user) {
      throw new Error('بيانات الدخول غير صحيحة')
    }

    if (!user.is_active) {
      throw new Error('الحساب غير مفعل')
    }

    // حفظ معلومات الجلسة في localStorage
    localStorage.setItem('currentUser', JSON.stringify(user))

    return { user }
  } catch (error) {
    throw error
  }
}

// دالة لتسجيل الخروج
export const signOut = async () => {
  try {
    localStorage.removeItem('currentUser')
    return true
  } catch (error) {
    throw new Error('خطأ في تسجيل الخروج')
  }
}

// دالة للحصول على المستخدم الحالي
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('currentUser')
    return userStr ? JSON.parse(userStr) : null
  } catch (error) {
    console.error('خطأ في الحصول على المستخدم الحالي:', error)
    return null
  }
}

// دالة للحصول على بيانات المستخدم من قاعدة البيانات
export const getUserProfile = (userId: string) => {
  try {
    return dal.users.getById(userId)
  } catch (error) {
    throw new Error('خطأ في الحصول على بيانات المستخدم')
  }
}

// دالة لتحديث بيانات المستخدم
export const updateUserProfile = (userId: string, updates: any) => {
  try {
    return dal.users.updateProfile(userId, updates)
  } catch (error) {
    throw new Error('خطأ في تحديث بيانات المستخدم')
  }
}

// دالة للحصول على إعدادات النظام
export const getSystemSettings = () => {
  try {
    const stmt = db.prepare('SELECT * FROM system_settings ORDER BY setting_key')
    return stmt.all()
  } catch (error) {
    throw new Error('خطأ في الحصول على إعدادات النظام')
  }
}

// دالة لتحديث إعدادات النظام
export const updateSystemSetting = (key: string, value: string, settingType: string = 'string') => {
  try {
    const stmt = db.prepare(`
      INSERT OR REPLACE INTO system_settings (setting_key, setting_value, setting_type, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `)

    stmt.run(key, value, settingType)

    // إرجاع الإعداد المحدث
    const getStmt = db.prepare('SELECT * FROM system_settings WHERE setting_key = ?')
    return getStmt.get(key)
  } catch (error) {
    throw new Error('خطأ في تحديث إعدادات النظام')
  }
}

// دالة مساعدة للحصول على معلومات النظام
export const getSystemInfo = () => {
  const dbInfo = getDatabaseInfo()
  return {
    database: dbInfo,
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  }
}
