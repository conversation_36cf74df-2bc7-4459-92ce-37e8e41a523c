import Database from 'better-sqlite3'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'

// مسار قاعدة البيانات
const isDev = process.env.NODE_ENV === 'development'
const dbPath = isDev 
  ? path.join(process.cwd(), 'data', 'aridoo-pos.db')
  : path.join(app.getPath('userData'), 'aridoo-pos.db')

// التأكد من وجود مجلد البيانات
const dbDir = path.dirname(dbPath)
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true })
}

// إنشاء اتصال قاعدة البيانات
export const db = new Database(dbPath, {
  verbose: isDev ? console.log : undefined,
  fileMustExist: false
})

// تفعيل الخصائص المطلوبة
db.pragma('foreign_keys = ON')
db.pragma('journal_mode = WAL')
db.pragma('synchronous = NORMAL')
db.pragma('temp_store = memory')
db.pragma('mmap_size = 268435456') // 256MB

// دالة لتهيئة قاعدة البيانات
export const initializeDatabase = () => {
  try {
    // قراءة ملف المخطط
    const schemaPath = path.join(__dirname, '../database/sqlite-schema.sql')
    const schema = fs.readFileSync(schemaPath, 'utf8')
    
    // تنفيذ المخطط
    db.exec(schema)
    
    // إدراج البيانات الأولية إذا لم تكن موجودة
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number }
    if (userCount.count === 0) {
      insertSeedData()
    }
    
    console.log('تم تهيئة قاعدة البيانات بنجاح')
    return true
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات:', error)
    return false
  }
}

// دالة لإدراج البيانات الأولية من ملف SQL
const insertSeedData = () => {
  try {
    console.log('إدراج البيانات الأولية...')

    // قراءة ملف البيانات الأولية
    const fs = require('fs')
    const path = require('path')

    const seedFilePath = path.join(__dirname, '../database/sqlite-seed.sql')

    if (fs.existsSync(seedFilePath)) {
      const seedSQL = fs.readFileSync(seedFilePath, 'utf8')

      // تقسيم الاستعلامات وتنفيذها
      const statements = seedSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

      const transaction = db.transaction(() => {
        for (const statement of statements) {
          if (statement.trim()) {
            try {
              db.exec(statement)
            } catch (error) {
              // تجاهل أخطاء البيانات المكررة
              if (!error.message.includes('UNIQUE constraint failed')) {
                console.warn('تحذير في تنفيذ الاستعلام:', error.message)
              }
            }
          }
        }
      })

      transaction()
      console.log('تم إدراج البيانات الأولية بنجاح من الملف')
    } else {
      console.warn('ملف البيانات الأولية غير موجود، سيتم إنشاء بيانات أساسية')

      const transaction = db.transaction(() => {
        // إدراج المستخدم الإداري الافتراضي فقط
        db.prepare(`
          INSERT INTO users (id, email, password_hash, full_name, phone, role, is_active)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(
          '00000000-0000-0000-0000-000000000001',
          '<EMAIL>',
          '$2b$10$example_hash_here',
          'مدير النظام',
          '+964770000000',
          'admin',
          1
        )

        // إدراج إعدادات أساسية
        const insertSetting = db.prepare(`
          INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public)
          VALUES (?, ?, ?, ?, ?)
        `)

        insertSetting.run('company_name', 'متجر أريدوو', 'string', 'اسم الشركة', 1)
        insertSetting.run('currency', 'IQD', 'string', 'العملة المستخدمة', 1)
        insertSetting.run('language', 'ar', 'string', 'لغة النظام', 1)
      })

      transaction()
    }

    console.log('تم إدراج البيانات الأولية بنجاح')
  } catch (error) {
    console.error('خطأ في إدراج البيانات الأولية:', error)
    throw error
  }
}

// دالة للحصول على معلومات قاعدة البيانات
export const getDatabaseInfo = () => {
  try {
    const info = {
      path: dbPath,
      size: fs.statSync(dbPath).size,
      tables: db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `).all(),
      version: db.prepare('PRAGMA user_version').get(),
      pageCount: db.prepare('PRAGMA page_count').get(),
      pageSize: db.prepare('PRAGMA page_size').get()
    }
    return info
  } catch (error) {
    console.error('خطأ في الحصول على معلومات قاعدة البيانات:', error)
    return null
  }
}

// دالة لإنشاء نسخة احتياطية
export const createBackup = (backupPath?: string) => {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const defaultBackupPath = path.join(
      path.dirname(dbPath), 
      `backup-${timestamp}.db`
    )
    
    const targetPath = backupPath || defaultBackupPath
    
    // إنشاء نسخة احتياطية
    db.backup(targetPath)
    
    console.log(`تم إنشاء نسخة احتياطية: ${targetPath}`)
    return targetPath
  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error)
    throw error
  }
}

// دالة لاستعادة النسخة الاحتياطية
export const restoreBackup = (backupPath: string) => {
  try {
    // إغلاق الاتصال الحالي
    db.close()
    
    // نسخ ملف النسخة الاحتياطية
    fs.copyFileSync(backupPath, dbPath)
    
    // إعادة فتح الاتصال
    const newDb = new Database(dbPath)
    
    console.log(`تم استعادة النسخة الاحتياطية من: ${backupPath}`)
    return true
  } catch (error) {
    console.error('خطأ في استعادة النسخة الاحتياطية:', error)
    return false
  }
}

// دالة لتنظيف قاعدة البيانات
export const optimizeDatabase = () => {
  try {
    db.exec('VACUUM')
    db.exec('ANALYZE')
    console.log('تم تحسين قاعدة البيانات بنجاح')
    return true
  } catch (error) {
    console.error('خطأ في تحسين قاعدة البيانات:', error)
    return false
  }
}

// دالة لإغلاق قاعدة البيانات
export const closeDatabase = () => {
  try {
    db.close()
    console.log('تم إغلاق قاعدة البيانات')
  } catch (error) {
    console.error('خطأ في إغلاق قاعدة البيانات:', error)
  }
}

// تهيئة قاعدة البيانات عند تحميل الوحدة
initializeDatabase()
