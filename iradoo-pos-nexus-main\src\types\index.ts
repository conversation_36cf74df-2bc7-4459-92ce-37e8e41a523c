// أنواع إضافية للتطبيق
// Additional Application Types

export * from './database'

// أنواع واجهة المستخدم
export interface MenuItem {
  id: string
  title: string
  icon?: string
  path?: string
  children?: MenuItem[]
  permission?: string
}

// أنواع النماذج
export interface FormField {
  name: string
  label: string
  type: 'text' | 'number' | 'email' | 'password' | 'select' | 'textarea' | 'checkbox' | 'date'
  required?: boolean
  placeholder?: string
  options?: { value: string; label: string }[]
  validation?: any
}

// أنواع التقارير
export interface ReportConfig {
  title: string
  type: 'sales' | 'inventory' | 'customers' | 'suppliers' | 'financial'
  dateRange?: {
    from: string
    to: string
  }
  filters?: Record<string, any>
  groupBy?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ReportData {
  headers: string[]
  rows: any[][]
  summary?: Record<string, number>
  charts?: ChartData[]
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'doughnut'
  title: string
  data: {
    labels: string[]
    datasets: {
      label: string
      data: number[]
      backgroundColor?: string[]
      borderColor?: string[]
    }[]
  }
}

// أنواع نقطة البيع
export interface CartItem {
  product: Product
  quantity: number
  unit_price: number
  discount_amount: number
  line_total: number
}

export interface Cart {
  items: CartItem[]
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
}

export interface PaymentInfo {
  method: 'cash' | 'card' | 'credit' | 'mixed'
  amount_paid: number
  change_amount: number
  card_details?: {
    last_four: string
    type: string
  }
}

// أنواع الإشعارات
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

// أنواع البحث والتصفية
export interface SearchFilters {
  query?: string
  category?: string
  supplier?: string
  status?: string
  date_from?: string
  date_to?: string
  price_min?: number
  price_max?: number
  in_stock?: boolean
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  total_pages: number
}

export interface ApiResponse<T> {
  data: T
  pagination?: PaginationInfo
  message?: string
  success: boolean
}

// أنواع الإعدادات
export interface AppSettings {
  company_name: string
  company_address: string
  company_phone: string
  company_email: string
  currency: string
  tax_rate: number
  receipt_footer: string
  low_stock_alert: boolean
  auto_backup: boolean
  theme: 'light' | 'dark' | 'auto'
  language: 'ar' | 'en' | 'ku'
}

// أنواع المصادقة
export interface AuthUser {
  id: string
  email: string
  full_name: string
  role: 'admin' | 'manager' | 'employee'
  permissions: string[]
}

export interface LoginCredentials {
  email: string
  password: string
  remember_me?: boolean
}

// أنواع الأخطاء
export interface AppError {
  code: string
  message: string
  details?: any
}

// أنواع التصدير والاستيراد
export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'
  include_headers: boolean
  date_range?: {
    from: string
    to: string
  }
  filters?: Record<string, any>
}

export interface ImportResult {
  success_count: number
  error_count: number
  errors: {
    row: number
    message: string
  }[]
}

// أنواع النسخ الاحتياطي
export interface BackupInfo {
  id: string
  filename: string
  size: number
  created_at: string
  type: 'manual' | 'automatic'
  status: 'completed' | 'failed' | 'in_progress'
}

// أنواع الإحصائيات
export interface DashboardStats {
  today_sales: {
    count: number
    amount: number
  }
  monthly_sales: {
    count: number
    amount: number
  }
  low_stock_items: number
  total_customers: number
  total_products: number
  recent_sales: Sale[]
  top_products: {
    product: Product
    quantity_sold: number
    revenue: number
  }[]
}

// أنواع الطباعة
export interface PrintOptions {
  printer_name?: string
  paper_size: 'A4' | 'thermal_80mm' | 'thermal_58mm'
  copies: number
  auto_print: boolean
}

export interface ReceiptData {
  sale: Sale
  items: SaleItem[]
  customer?: Customer
  company_info: {
    name: string
    address: string
    phone: string
  }
  payment_info: PaymentInfo
}
