-- Migration 003: Reports and System Settings
-- تاريخ الإنشاء: 2025-06-28
-- الوصف: إنشاء جداول التقارير وإعدادات النظام

-- إن<PERSON>اء جدول التقارير المحفوظة
CREATE TABLE IF NOT EXISTS saved_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    report_type VARCHAR(100) NOT NULL,
    parameters JSONB,
    user_id UUID REFERENCES users(id),
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON><PERSON><PERSON> جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول سجل العمليات
CREATE TABLE IF NOT EXISTS activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس للتقارير والإعدادات
CREATE INDEX IF NOT EXISTS idx_saved_reports_user ON saved_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_reports_type ON saved_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_table ON activity_logs(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_date ON activity_logs(created_at);

-- إنشاء المشغلات لتحديث updated_at
CREATE TRIGGER update_saved_reports_updated_at BEFORE UPDATE ON saved_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- دالة لتسجيل العمليات تلقائياً
CREATE OR REPLACE FUNCTION log_activity()
RETURNS TRIGGER AS $$
DECLARE
    user_id_val UUID;
    action_val VARCHAR(100);
BEGIN
    -- تحديد نوع العملية
    IF TG_OP = 'INSERT' THEN
        action_val := 'CREATE';
    ELSIF TG_OP = 'UPDATE' THEN
        action_val := 'UPDATE';
    ELSIF TG_OP = 'DELETE' THEN
        action_val := 'DELETE';
    END IF;
    
    -- محاولة الحصول على معرف المستخدم من السجل
    IF TG_OP = 'DELETE' THEN
        user_id_val := OLD.user_id;
    ELSE
        user_id_val := NEW.user_id;
    END IF;
    
    -- تسجيل العملية
    INSERT INTO activity_logs (
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values
    ) VALUES (
        user_id_val,
        action_val,
        TG_TABLE_NAME,
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.id
            ELSE NEW.id
        END,
        CASE 
            WHEN TG_OP = 'DELETE' THEN row_to_json(OLD)
            WHEN TG_OP = 'UPDATE' THEN row_to_json(OLD)
            ELSE NULL
        END,
        CASE 
            WHEN TG_OP = 'INSERT' THEN row_to_json(NEW)
            WHEN TG_OP = 'UPDATE' THEN row_to_json(NEW)
            ELSE NULL
        END
    );
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغلات تسجيل العمليات للجداول المهمة
CREATE TRIGGER log_users_activity
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION log_activity();

CREATE TRIGGER log_products_activity
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH ROW EXECUTE FUNCTION log_activity();

CREATE TRIGGER log_sales_activity
    AFTER INSERT OR UPDATE OR DELETE ON sales
    FOR EACH ROW EXECUTE FUNCTION log_activity();

CREATE TRIGGER log_purchases_activity
    AFTER INSERT OR UPDATE OR DELETE ON purchases
    FOR EACH ROW EXECUTE FUNCTION log_activity();

-- دالة للحصول على إحصائيات المبيعات
CREATE OR REPLACE FUNCTION get_sales_stats(
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    total_sales BIGINT,
    total_amount DECIMAL(15,2),
    avg_sale_amount DECIMAL(15,2),
    top_product_id UUID,
    top_product_name VARCHAR(255),
    top_product_quantity BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH sales_summary AS (
        SELECT 
            COUNT(*) as sale_count,
            SUM(s.total_amount) as total_amt,
            AVG(s.total_amount) as avg_amt
        FROM sales s
        WHERE s.sale_date::DATE BETWEEN start_date AND end_date
        AND s.status = 'completed'
    ),
    top_product AS (
        SELECT 
            p.id,
            p.name,
            SUM(si.quantity) as total_qty
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        JOIN products p ON si.product_id = p.id
        WHERE s.sale_date::DATE BETWEEN start_date AND end_date
        AND s.status = 'completed'
        GROUP BY p.id, p.name
        ORDER BY total_qty DESC
        LIMIT 1
    )
    SELECT 
        ss.sale_count,
        ss.total_amt,
        ss.avg_amt,
        tp.id,
        tp.name,
        tp.total_qty
    FROM sales_summary ss
    CROSS JOIN top_product tp;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على المنتجات منخفضة المخزون
CREATE OR REPLACE FUNCTION get_low_stock_products()
RETURNS TABLE (
    product_id UUID,
    product_code VARCHAR(50),
    product_name VARCHAR(255),
    current_stock INTEGER,
    min_stock_level INTEGER,
    reorder_point INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.product_code,
        p.name,
        i.current_stock,
        p.min_stock_level,
        p.reorder_point
    FROM products p
    JOIN inventory i ON p.id = i.product_id
    WHERE i.current_stock <= p.reorder_point
    AND p.is_active = true
    ORDER BY (i.current_stock::FLOAT / NULLIF(p.reorder_point, 0)) ASC;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على أفضل العملاء
CREATE OR REPLACE FUNCTION get_top_customers(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    customer_id UUID,
    customer_name VARCHAR(255),
    total_purchases BIGINT,
    total_amount DECIMAL(15,2),
    avg_purchase_amount DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.name,
        COUNT(s.id) as purchase_count,
        SUM(s.total_amount) as total_amt,
        AVG(s.total_amount) as avg_amt
    FROM customers c
    JOIN sales s ON c.id = s.customer_id
    WHERE s.sale_date::DATE BETWEEN start_date AND end_date
    AND s.status = 'completed'
    GROUP BY c.id, c.name
    ORDER BY total_amt DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- إدراج إعدادات النظام الافتراضية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('company_name', 'متجر أريدوو', 'string', 'اسم الشركة', true),
('company_address', 'بغداد، العراق', 'string', 'عنوان الشركة', true),
('company_phone', '+************', 'string', 'هاتف الشركة', true),
('company_email', '<EMAIL>', 'string', 'بريد الشركة الإلكتروني', true),
('currency', 'IQD', 'string', 'العملة المستخدمة', true),
('tax_rate', '0', 'number', 'معدل الضريبة الافتراضي', true),
('receipt_footer', 'شكراً لزيارتكم - أريدوو', 'string', 'نص أسفل الفاتورة', true),
('low_stock_alert', 'true', 'boolean', 'تنبيه المخزون المنخفض', false),
('auto_backup', 'true', 'boolean', 'النسخ الاحتياطي التلقائي', false),
('theme', 'light', 'string', 'سمة التطبيق', true),
('language', 'ar', 'string', 'لغة التطبيق', true),
('receipt_printer', '', 'string', 'طابعة الفواتير الافتراضية', false),
('barcode_scanner', 'true', 'boolean', 'تفعيل قارئ الباركود', false)
ON CONFLICT (setting_key) DO NOTHING;
