import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowRight, 
  Plus, 
  Minus, 
  Trash2, 
  Calculator,
  CreditCard,
  Banknote,
  Receipt
} from 'lucide-react'

interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  total: number
}

const Sales = () => {
  const [cart, setCart] = useState<CartItem[]>([])
  const [total, setTotal] = useState(0)

  // Sample products for demo
  const sampleProducts = [
    { id: '1', name: 'قلم أزرق', price: 2.50, barcode: '*********' },
    { id: '2', name: 'دفتر A4', price: 15.00, barcode: '*********' },
    { id: '3', name: 'مبراة', price: 3.75, barcode: '*********' },
    { id: '4', name: 'مسطرة 30 سم', price: 8.25, barcode: '*********' },
  ]

  const addToCart = (product: any) => {
    const existingItem = cart.find(item => item.id === product.id)
    
    if (existingItem) {
      updateQuantity(product.id, existingItem.quantity + 1)
    } else {
      const newItem: CartItem = {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        total: product.price
      }
      setCart([...cart, newItem])
      setTotal(total + product.price)
    }
  }

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(id)
      return
    }

    const updatedCart = cart.map(item => {
      if (item.id === id) {
        return {
          ...item,
          quantity: newQuantity,
          total: item.price * newQuantity
        }
      }
      return item
    })
    
    setCart(updatedCart)
    const newTotal = updatedCart.reduce((sum, item) => sum + item.total, 0)
    setTotal(newTotal)
  }

  const removeFromCart = (id: string) => {
    const updatedCart = cart.filter(item => item.id !== id)
    setCart(updatedCart)
    const newTotal = updatedCart.reduce((sum, item) => sum + item.total, 0)
    setTotal(newTotal)
  }

  const clearCart = () => {
    setCart([])
    setTotal(0)
  }

  const processPayment = (method: string) => {
    alert(`تم الدفع بنجاح بواسطة ${method}\nالإجمالي: ${total.toFixed(2)} ر.س`)
    clearCart()
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                <ArrowRight className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-800">نقطة البيع</h1>
          </div>
          <Badge variant="secondary" className="text-lg px-4 py-2">
            فاتورة جديدة
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Products Section */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>المنتجات المتاحة</CardTitle>
                <CardDescription>اختر المنتجات لإضافتها للفاتورة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {sampleProducts.map(product => (
                    <Card key={product.id} className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => addToCart(product)}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-semibold">{product.name}</h3>
                            <p className="text-sm text-gray-500">#{product.barcode}</p>
                          </div>
                          <div className="text-left">
                            <p className="text-lg font-bold text-green-600">
                              {product.price.toFixed(2)} ر.س
                            </p>
                            <Button size="sm" className="mt-2">
                              <Plus className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Cart Section */}
          <div>
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>سلة المشتريات</span>
                  {cart.length > 0 && (
                    <Button variant="outline" size="sm" onClick={clearCart}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {cart.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    السلة فارغة
                  </div>
                ) : (
                  <div className="space-y-4">
                    {cart.map(item => (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-gray-500">
                            {item.price.toFixed(2)} ر.س × {item.quantity}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline" 
                                  onClick={() => updateQuantity(item.id, item.quantity - 1)}>
                            <Minus className="w-3 h-3" />
                          </Button>
                          <span className="w-8 text-center">{item.quantity}</span>
                          <Button size="sm" variant="outline"
                                  onClick={() => updateQuantity(item.id, item.quantity + 1)}>
                            <Plus className="w-3 h-3" />
                          </Button>
                        </div>
                        <div className="text-left ml-4">
                          <p className="font-bold">{item.total.toFixed(2)} ر.س</p>
                        </div>
                      </div>
                    ))}

                    {/* Total */}
                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center text-xl font-bold">
                        <span>الإجمالي:</span>
                        <span className="text-green-600">{total.toFixed(2)} ر.س</span>
                      </div>
                    </div>

                    {/* Payment Buttons */}
                    <div className="space-y-2 pt-4">
                      <Button className="w-full" size="lg" 
                              onClick={() => processPayment('نقداً')}>
                        <Banknote className="w-5 h-5 ml-2" />
                        دفع نقداً
                      </Button>
                      <Button variant="outline" className="w-full" size="lg"
                              onClick={() => processPayment('بطاقة ائتمان')}>
                        <CreditCard className="w-5 h-5 ml-2" />
                        دفع بالبطاقة
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sales
