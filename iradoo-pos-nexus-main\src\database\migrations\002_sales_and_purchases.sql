-- Migration 002: Sales and Purchases Tables
-- تاريخ الإنشاء: 2025-06-28
-- الوصف: إنشاء جداول المبيعات والمشتريات

-- إ<PERSON><PERSON><PERSON>ء جدول المبيعات
CREATE TABLE IF NOT EXISTS sales (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES customers(id),
    user_id UUID REFERENCES users(id) NOT NULL,
    sale_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    change_amount DECIMAL(15,2) DEFAULT 0,
    payment_method VARCHAR(50) DEFAULT 'cash' CHECK (payment_method IN ('cash', 'card', 'credit', 'mixed')),
    status VARCHAR(50) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'refunded')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تفاصيل المبيعات
CREATE TABLE IF NOT EXISTS sale_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sale_id UUID REFERENCES sales(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المشتريات
CREATE TABLE IF NOT EXISTS purchases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    purchase_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES suppliers(id) NOT NULL,
    user_id UUID REFERENCES users(id) NOT NULL,
    purchase_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expected_delivery_date DATE,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'received', 'partial', 'cancelled')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تفاصيل المشتريات
CREATE TABLE IF NOT EXISTS purchase_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    purchase_id UUID REFERENCES purchases(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity_ordered INTEGER NOT NULL,
    quantity_received INTEGER DEFAULT 0,
    unit_cost DECIMAL(15,2) NOT NULL,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    line_total DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    reference_type VARCHAR(50) NOT NULL CHECK (reference_type IN ('sale', 'purchase', 'customer', 'supplier')),
    reference_id UUID NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('cash', 'card', 'bank_transfer', 'check')),
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    user_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس للمبيعات والمشتريات
CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_user ON sales(user_id);
CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date);
CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(status);
CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id);

CREATE INDEX IF NOT EXISTS idx_purchases_supplier ON purchases(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchases_user ON purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchase_date);
CREATE INDEX IF NOT EXISTS idx_purchases_status ON purchases(status);
CREATE INDEX IF NOT EXISTS idx_purchase_items_purchase ON purchase_items(purchase_id);
CREATE INDEX IF NOT EXISTS idx_purchase_items_product ON purchase_items(product_id);

CREATE INDEX IF NOT EXISTS idx_payments_reference ON payments(reference_type, reference_id);
CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_payments_user ON payments(user_id);

-- إنشاء المشغلات لتحديث updated_at
CREATE TRIGGER update_sales_updated_at BEFORE UPDATE ON sales
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchases_updated_at BEFORE UPDATE ON purchases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- دالة لتوليد رقم المبيعة التلقائي
CREATE OR REPLACE FUNCTION generate_sale_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    sale_number TEXT;
BEGIN
    -- الحصول على آخر رقم مبيعة
    SELECT COALESCE(MAX(CAST(SUBSTRING(sale_number FROM 6) AS INTEGER)), 0) + 1
    INTO next_number
    FROM sales
    WHERE sale_number LIKE 'SALE-%';
    
    -- تكوين رقم المبيعة الجديد
    sale_number := 'SALE-' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN sale_number;
END;
$$ LANGUAGE plpgsql;

-- دالة لتوليد رقم المشتريات التلقائي
CREATE OR REPLACE FUNCTION generate_purchase_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    purchase_number TEXT;
BEGIN
    -- الحصول على آخر رقم مشتريات
    SELECT COALESCE(MAX(CAST(SUBSTRING(purchase_number FROM 6) AS INTEGER)), 0) + 1
    INTO next_number
    FROM purchases
    WHERE purchase_number LIKE 'PURCH-%';
    
    -- تكوين رقم المشتريات الجديد
    purchase_number := 'PURCH-' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN purchase_number;
END;
$$ LANGUAGE plpgsql;

-- دالة لتوليد رقم الدفعة التلقائي
CREATE OR REPLACE FUNCTION generate_payment_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    payment_number TEXT;
BEGIN
    -- الحصول على آخر رقم دفعة
    SELECT COALESCE(MAX(CAST(SUBSTRING(payment_number FROM 5) AS INTEGER)), 0) + 1
    INTO next_number
    FROM payments
    WHERE payment_number LIKE 'PAY-%';
    
    -- تكوين رقم الدفعة الجديد
    payment_number := 'PAY-' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN payment_number;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث المخزون عند المبيعة
CREATE OR REPLACE FUNCTION update_inventory_on_sale()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- تقليل المخزون عند إضافة عنصر مبيعة
        UPDATE inventory 
        SET current_stock = current_stock - NEW.quantity,
            last_updated = NOW()
        WHERE product_id = NEW.product_id;
        
        -- إضافة حركة مخزون
        INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, user_id)
        SELECT NEW.product_id, 'out', NEW.quantity, 'sale', NEW.sale_id, s.user_id
        FROM sales s WHERE s.id = NEW.sale_id;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث المخزون عند المشتريات
CREATE OR REPLACE FUNCTION update_inventory_on_purchase()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' AND OLD.quantity_received != NEW.quantity_received THEN
        -- زيادة المخزون عند استلام البضاعة
        UPDATE inventory 
        SET current_stock = current_stock + (NEW.quantity_received - OLD.quantity_received),
            last_updated = NOW()
        WHERE product_id = NEW.product_id;
        
        -- إضافة حركة مخزون
        INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, user_id)
        SELECT NEW.product_id, 'in', (NEW.quantity_received - OLD.quantity_received), 'purchase', NEW.purchase_id, p.user_id
        FROM purchases p WHERE p.id = NEW.purchase_id;
        
        RETURN NEW;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المشغلات لتحديث المخزون
CREATE TRIGGER trigger_update_inventory_on_sale
    AFTER INSERT ON sale_items
    FOR EACH ROW EXECUTE FUNCTION update_inventory_on_sale();

CREATE TRIGGER trigger_update_inventory_on_purchase
    AFTER UPDATE ON purchase_items
    FOR EACH ROW EXECUTE FUNCTION update_inventory_on_purchase();
