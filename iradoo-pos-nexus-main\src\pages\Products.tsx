import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowRight, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Package,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

interface Product {
  id: string
  name: string
  price: number
  cost: number
  stock: number
  category: string
  barcode: string
  status: 'active' | 'inactive'
}

const Products = () => {
  const [products, setProducts] = useState<Product[]>([
    {
      id: '1',
      name: 'قلم أزرق',
      price: 2.50,
      cost: 1.00,
      stock: 150,
      category: 'قرطاسية',
      barcode: '123456789',
      status: 'active'
    },
    {
      id: '2',
      name: 'دفتر A4',
      price: 15.00,
      cost: 8.00,
      stock: 25,
      category: 'قرطاسية',
      barcode: '987654321',
      status: 'active'
    },
    {
      id: '3',
      name: 'مبراة',
      price: 3.75,
      cost: 1.50,
      stock: 5,
      category: 'قرطاسية',
      barcode: '456789123',
      status: 'active'
    },
    {
      id: '4',
      name: 'مسطرة 30 سم',
      price: 8.25,
      cost: 4.00,
      stock: 0,
      category: 'قرطاسية',
      barcode: '789123456',
      status: 'inactive'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.barcode.includes(searchTerm)
  )

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { label: 'نفد المخزون', color: 'destructive' }
    if (stock <= 10) return { label: 'مخزون منخفض', color: 'secondary' }
    return { label: 'متوفر', color: 'default' }
  }

  const deleteProduct = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      setProducts(products.filter(p => p.id !== id))
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                <ArrowRight className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </Button>
            </Link>
            <h1 className="text-3xl font-bold text-gray-800">إدارة المنتجات</h1>
          </div>
          <Button onClick={() => setShowAddForm(true)}>
            <Plus className="w-4 h-4 ml-2" />
            إضافة منتج جديد
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold">{products.length}</p>
                </div>
                <Package className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">المنتجات النشطة</p>
                  <p className="text-2xl font-bold text-green-600">
                    {products.filter(p => p.status === 'active').length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">مخزون منخفض</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {products.filter(p => p.stock <= 10 && p.stock > 0).length}
                  </p>
                </div>
                <AlertTriangle className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">نفد المخزون</p>
                  <p className="text-2xl font-bold text-red-600">
                    {products.filter(p => p.stock === 0).length}
                  </p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث عن منتج بالاسم أو الباركود..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Products Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المنتجات</CardTitle>
            <CardDescription>
              إدارة وتعديل المنتجات والمخزون
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3">اسم المنتج</th>
                    <th className="text-right p-3">الباركود</th>
                    <th className="text-right p-3">الفئة</th>
                    <th className="text-right p-3">سعر التكلفة</th>
                    <th className="text-right p-3">سعر البيع</th>
                    <th className="text-right p-3">المخزون</th>
                    <th className="text-right p-3">الحالة</th>
                    <th className="text-right p-3">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map(product => {
                    const stockStatus = getStockStatus(product.stock)
                    return (
                      <tr key={product.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-medium">{product.name}</td>
                        <td className="p-3 text-gray-600">{product.barcode}</td>
                        <td className="p-3">{product.category}</td>
                        <td className="p-3">{product.cost.toFixed(2)} ر.س</td>
                        <td className="p-3 font-semibold">{product.price.toFixed(2)} ر.س</td>
                        <td className="p-3">
                          <Badge variant={stockStatus.color as any}>
                            {product.stock} - {stockStatus.label}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                            {product.status === 'active' ? 'نشط' : 'غير نشط'}
                          </Badge>
                        </td>
                        <td className="p-3">
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="outline" 
                                    onClick={() => deleteProduct(product.id)}>
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد منتجات تطابق البحث
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default Products
