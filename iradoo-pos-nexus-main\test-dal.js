// اختبار طبقة الوصول للبيانات (DAL)
import { initializeDatabase } from './src/lib/database.js';

console.log('🔍 اختبار طبقة الوصول للبيانات...');

try {
  // تهيئة قاعدة البيانات
  console.log('🚀 تهيئة قاعدة البيانات...');
  const dbInitialized = initializeDatabase();
  
  if (dbInitialized) {
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
  } else {
    console.log('❌ فشل في تهيئة قاعدة البيانات');
    process.exit(1);
  }
  
  // استيراد DAL classes
  console.log('📦 استيراد طبقة الوصول للبيانات...');
  
  // نظراً لأن DAL classes تستخدم ES modules، سنحتاج لاستيرادها ديناميكياً
  const { getAllDALInstances } = await import('./src/lib/dal/index.js');
  const dal = getAllDALInstances();
  
  console.log('✅ تم استيراد DAL بنجاح');
  
  // اختبار Users DAL
  console.log('\n👤 اختبار إدارة المستخدمين:');
  
  // البحث عن المستخدم الإداري
  const adminUser = dal.users.getByEmail('<EMAIL>');
  if (adminUser) {
    console.log(`  ✅ تم العثور على المستخدم الإداري: ${adminUser.full_name}`);
  } else {
    console.log('  ❌ لم يتم العثور على المستخدم الإداري');
  }
  
  // اختبار إنشاء مستخدم جديد
  try {
    const newUser = dal.users.create({
      email: '<EMAIL>',
      password_hash: '$2b$10$test_hash',
      full_name: 'مستخدم تجريبي',
      phone: '+964771234567',
      role: 'employee'
    });
    console.log(`  ✅ تم إنشاء مستخدم جديد: ${newUser.full_name}`);
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      console.log('  ℹ️ المستخدم التجريبي موجود مسبقاً');
    } else {
      console.log(`  ❌ خطأ في إنشاء المستخدم: ${error.message}`);
    }
  }
  
  // عرض جميع المستخدمين
  const allUsers = dal.users.getAll();
  console.log(`  📊 إجمالي المستخدمين: ${allUsers.length}`);
  
  // اختبار Products DAL
  console.log('\n📦 اختبار إدارة المنتجات:');
  
  try {
    // إنشاء منتج تجريبي
    const newProduct = dal.products.create({
      product_code: 'TEST001',
      barcode: '1234567890999',
      name: 'منتج تجريبي',
      description: 'منتج للاختبار',
      unit_of_measure: 'piece',
      cost_price: 10000,
      selling_price: 15000,
      min_stock_level: 5,
      reorder_point: 10,
      tax_rate: 0
    });
    console.log(`  ✅ تم إنشاء منتج جديد: ${newProduct.name}`);
  } catch (error) {
    if (error.message.includes('UNIQUE constraint failed')) {
      console.log('  ℹ️ المنتج التجريبي موجود مسبقاً');
    } else {
      console.log(`  ❌ خطأ في إنشاء المنتج: ${error.message}`);
    }
  }
  
  // عرض جميع المنتجات
  const allProducts = dal.products.getAll();
  console.log(`  📊 إجمالي المنتجات: ${allProducts.length}`);
  
  // اختبار البحث في المنتجات
  const searchResults = dal.products.search('تجريبي');
  console.log(`  🔍 نتائج البحث عن "تجريبي": ${searchResults.length} منتج`);
  
  // اختبار Sales DAL
  console.log('\n💰 اختبار إدارة المبيعات:');
  
  try {
    // إنشاء فاتورة تجريبية
    const saleData = {
      customer_id: null, // عميل نقدي
      user_id: adminUser?.id || '00000000-0000-0000-0000-000000000001',
      sale_date: new Date().toISOString(),
      subtotal: 15000,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 15000,
      paid_amount: 15000,
      change_amount: 0,
      payment_method: 'cash',
      status: 'completed',
      notes: 'فاتورة تجريبية'
    };
    
    const newSale = dal.sales.create(saleData);
    console.log(`  ✅ تم إنشاء فاتورة جديدة: ${newSale.sale_number}`);
  } catch (error) {
    console.log(`  ❌ خطأ في إنشاء الفاتورة: ${error.message}`);
  }
  
  // عرض جميع المبيعات
  const allSales = dal.sales.getAll();
  console.log(`  📊 إجمالي المبيعات: ${allSales.length}`);
  
  // اختبار التقارير
  console.log('\n📈 اختبار التقارير:');
  
  const today = new Date().toISOString().split('T')[0];
  const salesReport = dal.sales.getSalesReport(today, today);
  console.log(`  📊 مبيعات اليوم: ${salesReport.total_sales} فاتورة بقيمة ${salesReport.total_amount} د.ع`);
  
  console.log('\n🎉 تم اختبار طبقة الوصول للبيانات بنجاح!');
  console.log('💡 جميع العمليات تعمل بشكل صحيح');
  
} catch (error) {
  console.error('❌ خطأ في اختبار DAL:', error);
  process.exit(1);
}
