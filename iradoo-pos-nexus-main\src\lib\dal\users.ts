import { BaseDAL } from './base'
import type { User } from '../../types/database'
import bcrypt from 'bcryptjs'

export class UsersDAL extends BaseDAL<User> {
  constructor() {
    super('users')
  }

  // دالة للحصول على مستخدم بالبريد الإلكتروني
  getByEmail(email: string): User | null {
    try {
      const stmt = this.db.prepare('SELECT * FROM users WHERE email = ?')
      return stmt.get(email) as User | null
    } catch (error) {
      console.error('خطأ في الحصول على المستخدم بالبريد الإلكتروني:', error)
      throw error
    }
  }

  // دالة لإنشاء مستخدم جديد
  createUser(userData: {
    email: string
    password: string
    full_name: string
    phone?: string
    role?: 'admin' | 'manager' | 'employee'
  }): User {
    try {
      // تشفير كلمة المرور
      const saltRounds = 10
      const password_hash = bcrypt.hashSync(userData.password, saltRounds)

      const newUser = {
        email: userData.email,
        password_hash,
        full_name: userData.full_name,
        phone: userData.phone || null,
        role: userData.role || 'employee',
        is_active: 1
      }

      return this.insert(newUser)
    } catch (error) {
      console.error('خطأ في إنشاء المستخدم:', error)
      throw error
    }
  }

  // دالة للتحقق من كلمة المرور
  verifyPassword(email: string, password: string): User | null {
    try {
      const user = this.getByEmail(email)
      if (!user) {
        return null
      }

      const isValid = bcrypt.compareSync(password, user.password_hash)
      return isValid ? user : null
    } catch (error) {
      console.error('خطأ في التحقق من كلمة المرور:', error)
      throw error
    }
  }

  // دالة لتغيير كلمة المرور
  changePassword(userId: string, newPassword: string): boolean {
    try {
      const saltRounds = 10
      const password_hash = bcrypt.hashSync(newPassword, saltRounds)

      const stmt = this.db.prepare(`
        UPDATE users 
        SET password_hash = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `)
      
      const result = stmt.run(password_hash, userId)
      return result.changes > 0
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error)
      throw error
    }
  }

  // دالة لتفعيل/إلغاء تفعيل المستخدم
  toggleUserStatus(userId: string): boolean {
    try {
      const user = this.getById(userId)
      if (!user) {
        return false
      }

      const newStatus = user.is_active ? 0 : 1
      const stmt = this.db.prepare(`
        UPDATE users 
        SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `)
      
      const result = stmt.run(newStatus, userId)
      return result.changes > 0
    } catch (error) {
      console.error('خطأ في تغيير حالة المستخدم:', error)
      throw error
    }
  }

  // دالة للحصول على المستخدمين النشطين
  getActiveUsers(): User[] {
    try {
      const stmt = this.db.prepare('SELECT * FROM users WHERE is_active = 1 ORDER BY full_name')
      return stmt.all() as User[]
    } catch (error) {
      console.error('خطأ في الحصول على المستخدمين النشطين:', error)
      throw error
    }
  }

  // دالة للحصول على المستخدمين حسب الدور
  getUsersByRole(role: 'admin' | 'manager' | 'employee'): User[] {
    try {
      const stmt = this.db.prepare('SELECT * FROM users WHERE role = ? AND is_active = 1 ORDER BY full_name')
      return stmt.all(role) as User[]
    } catch (error) {
      console.error('خطأ في الحصول على المستخدمين حسب الدور:', error)
      throw error
    }
  }

  // دالة للبحث في المستخدمين
  searchUsers(searchTerm: string): User[] {
    return this.search(searchTerm, ['full_name', 'email', 'phone'])
  }

  // دالة لتحديث ملف المستخدم
  updateProfile(userId: string, profileData: {
    full_name?: string
    phone?: string
    email?: string
  }): User | null {
    try {
      // التحقق من عدم تكرار البريد الإلكتروني
      if (profileData.email) {
        const existingUser = this.getByEmail(profileData.email)
        if (existingUser && existingUser.id !== userId) {
          throw new Error('البريد الإلكتروني مستخدم من قبل مستخدم آخر')
        }
      }

      return this.update(userId, profileData)
    } catch (error) {
      console.error('خطأ في تحديث ملف المستخدم:', error)
      throw error
    }
  }

  // دالة للحصول على إحصائيات المستخدمين
  getUserStats() {
    try {
      const totalUsers = this.count()
      const activeUsers = this.count({ is_active: 1 })
      const adminUsers = this.count({ role: 'admin', is_active: 1 })
      const managerUsers = this.count({ role: 'manager', is_active: 1 })
      const employeeUsers = this.count({ role: 'employee', is_active: 1 })

      return {
        total: totalUsers,
        active: activeUsers,
        inactive: totalUsers - activeUsers,
        byRole: {
          admin: adminUsers,
          manager: managerUsers,
          employee: employeeUsers
        }
      }
    } catch (error) {
      console.error('خطأ في الحصول على إحصائيات المستخدمين:', error)
      throw error
    }
  }

  // دالة للحصول على آخر نشاط للمستخدمين
  getUsersWithLastActivity() {
    try {
      const stmt = this.db.prepare(`
        SELECT u.*, 
               al.created_at as last_activity
        FROM users u
        LEFT JOIN (
          SELECT user_id, MAX(created_at) as created_at
          FROM activity_logs
          GROUP BY user_id
        ) al ON u.id = al.user_id
        WHERE u.is_active = 1
        ORDER BY al.created_at DESC NULLS LAST
      `)
      
      return stmt.all()
    } catch (error) {
      console.error('خطأ في الحصول على آخر نشاط للمستخدمين:', error)
      throw error
    }
  }
}

// إنشاء مثيل واحد للاستخدام
export const usersDAL = new UsersDAL()
